# 🚀 AI Healing - Quick Start Guide

## 1. Validate Setup (2 minutes)

```bash
# Test the integration
cd /Users/<USER>/Documents/automation-tool/mobileautotool-research/MobileAppAutomation
python3 test_ai_integration.py
```

Expected output:
```
🎉 All tests passed! AI integration is ready to use.
```

## 2. Minimal Integration (5 minutes)

Add to your existing action executor:

```python
# Add this import to your action executor file
from temp_ai_integration import AIHealingOrchestrator

class YourActionExecutor:
    def __init__(self):
        # Your existing initialization
        # ... 
        
        # Add AI healing
        self.ai_healing = AIHealingOrchestrator()
    
    def execute_action(self, driver, action_data):
        try:
            # Your existing action execution logic
            return self.original_action_logic(driver, action_data)
            
        except Exception as e:
            # NEW: Try AI healing on failure
            print(f"🩺 Attempting AI healing for failed action...")
            
            healing_session = self.ai_healing.heal_test_failure(
                driver=driver,
                failed_action=self._describe_action(action_data),
                error_message=str(e)
            )
            
            if healing_session.final_result.value == 'success':
                print(f"✅ AI healing successful!")
                return {'status': 'healed', 'original_error': str(e)}
            else:
                print(f"❌ AI healing failed, re-raising original error")
                raise e
    
    def _describe_action(self, action_data):
        # Convert your action_data to human readable description
        action_type = action_data.get('action_type', 'unknown')
        if action_type == 'click':
            return f"Click on {action_data.get('element', {}).get('text', 'element')}"
        # Add other action types as needed
        return f"Execute {action_type} action"
```

## 3. Test with a Real Failure (5 minutes)

Try it with a test that you know fails:

```python
# In your test file, add some debug info
try:
    result = your_action_executor.execute_action(driver, {
        'action_type': 'click',
        'locator': {'by': 'xpath', 'value': '//button[@text="Login"]'},
        'element': {'text': 'Login', 'type': 'button'}
    })
    
    if result.get('ai_healing_used'):
        print("🎉 AI healing saved the day!")
        print(f"Original error: {result['original_error']}")
        print(f"Healing took: {result.get('healing_time', 0):.2f} seconds")
        
except Exception as e:
    if hasattr(e, 'healing_info'):
        print("AI healing was attempted but couldn't fix this issue")
        print(f"Healing attempts: {e.healing_info['attempts']}")
    else:
        print("Regular test failure (no healing attempted)")
```

## 4. Monitor Results

Check the generated outputs:

```bash
# View healing logs
ls ai_healing_outputs/contexts/

# View healing attempts in your application logs
# Look for entries from 'temp_ai_integration.ai_healing_orchestrator'
```

## 5. Production Configuration

Update your `.env` file if needed:

```bash
# Adjust these based on your needs
AI_HEALING_MAX_ATTEMPTS=3          # Max healing attempts per failure
AI_HEALING_CONFIDENCE_THRESHOLD=0.6 # Min confidence to execute suggestions
SCREENSHOT_FREQUENCY_LIMIT=5.0      # Min seconds between screenshots
```

## 🎯 What to Expect

### First Success
When AI healing works, you'll see:
```
🩺 Attempting AI healing for failed action...
AI suggestion (confidence 0.85): Click on Sign In button
Successfully executed click on //XCUIElementTypeButton[@name='Sign In']
✅ AI healing successful!
```

### When It Helps Debug
Even when healing doesn't fix the issue, it provides insights:
```
AI suggestion (confidence 0.72): Element may be covered by keyboard
Try scrolling down to reveal the login button
```

### Common Success Scenarios
- **Element name changes**: "Login" vs "Sign In" 
- **Timing issues**: AI suggests waiting for elements
- **Keyboard interference**: AI detects keyboard blocking elements
- **Alternative paths**: AI finds different elements that achieve same goal

## 🛠️ Troubleshooting

### If healing always fails (confidence too low):
```python
# Temporarily lower threshold for testing
orchestrator = AIHealingOrchestrator(confidence_threshold=0.3)
```

### If screenshots fail:
```python
# Check driver state
print(f"Driver session: {driver.session_id}")
print(f"Current context: {driver.current_context}")
```

### If API calls fail:
```bash
# Test API key
python3 -c "from dotenv import load_dotenv; load_dotenv(); import os; print(os.getenv('TOGETHER_API_KEY'))"
```

## 📊 Success Metrics

After 1 week of usage, you should see:
- **Reduced manual test debugging time**
- **Fewer "flaky test" reports**  
- **Healing success rate** (aim for >30% initially)
- **Faster test recovery** (healing takes 10-30 seconds vs hours of debugging)

## 🔄 Next Steps

1. ✅ **Week 1**: Validate integration works with a few tests
2. ✅ **Week 2**: Expand to more test scenarios  
3. ✅ **Week 3**: Analyze patterns and adjust confidence thresholds
4. ✅ **Week 4**: Full rollout to test suites

---

**Need help?** Check `AI_INTEGRATION_SUMMARY.md` for detailed documentation or `temp_ai_integration/README.md` for advanced usage examples.