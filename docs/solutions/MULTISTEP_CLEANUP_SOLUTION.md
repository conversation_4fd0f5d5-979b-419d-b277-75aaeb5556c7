# MultiStep Cleanup Checkbox Solution

## Problem Summary

The Android UI only provided cleanup checkboxes for individual steps within multiStep actions, but the Android player logic expected the `cleanup: true` property at the multiStep action level to identify cleanup steps. This created a disconnect where users couldn't mark entire multiStep actions as cleanup steps through the UI.

## Root Cause

1. **UI Issue**: The multiStep action form (`app_android/static/js/modules/actionFormManager.js`) lacked a cleanup checkbox at the action level
2. **Logic Gap**: The action creation logic in `app_android/static/js/action-manager.js` didn't handle the cleanup checkbox for multiStep actions
3. **Display Issue**: The action description and visual indicators didn't show when a multiStep action was marked as cleanup
4. **Edit Issue**: The editAction function didn't populate the cleanup checkbox when editing existing multiStep actions

## Solution Implementation

### 1. Added Cleanup Checkbox to MultiStep Form

**File**: `app_android/static/js/modules/actionFormManager.js`

Added a cleanup checkbox to the multiStep action form:

```html
<!-- Cleanup checkbox for multiStep action -->
<div class="form-group mb-3">
    <div class="form-check">
        <input class="form-check-input" type="checkbox" id="${fullPrefix}multiStepCleanup" name="${fullPrefix}multiStepCleanup">
        <label class="form-check-label" for="${fullPrefix}multiStepCleanup">
            <i class="bi bi-broom"></i> Mark as Cleanup Step
        </label>
        <div class="form-text text-muted">
            Cleanup steps are executed during test failures and retries to restore the app to a clean state.
        </div>
    </div>
</div>
```

### 2. Updated Action Creation Logic

**File**: `app_android/static/js/action-manager.js`

Updated all multiStep cases in the action creation logic to handle the cleanup checkbox:

```javascript
// Handle cleanup checkbox for multiStep action
const multiStepCleanupCheckbox = document.getElementById('multiStepCleanup');
if (multiStepCleanupCheckbox) {
    updatedAction.cleanup = multiStepCleanupCheckbox.checked;
    if (multiStepCleanupCheckbox.checked) {
        this.app.logAction('info', 'MultiStep action marked as cleanup step');
    }
}
```

This was added to:
- Main multiStep action creation (line 3819)
- Action update logic (line 2213) 
- IfThenSteps "then" action cases (lines 1873 and 3352)

### 3. Enhanced Action Description

**File**: `app_android/static/js/action-description.js`

Updated the multiStep action description to show cleanup indicator:

```javascript
case 'multiStep':
    const testCaseName = actionData.test_case_name || 'Unknown Test Case';
    const stepsCount = actionData.test_case_steps_count ||
                      (actionData.test_case_steps ? actionData.test_case_steps.length : 0);
    const cleanupIndicator = actionData.cleanup ? ' [CLEANUP]' : '';
    return `Execute Test Case: ${testCaseName} (${stepsCount} steps)${cleanupIndicator}`;
```

### 4. Added Visual Cleanup Badge

**File**: `app_android/static/js/action-manager.js`

Added cleanup badge to action items when multiStep actions are marked as cleanup:

```javascript
${action.cleanup ? '<span class="badge bg-warning text-dark me-2" title="This action is marked as a cleanup step">🧹 Cleanup</span>' : ''}
```

This was added to both:
- `createActionItem` method (line 4208)
- Main action item creation (line 76)

### 5. Fixed Action Editing

**File**: `app_android/static/js/main.js`

Added multiStep case to the `editAction` function to populate form fields when editing:

```javascript
case 'multiStep':
    // Set the test case selection
    const multiStepTestCaseSelect = document.getElementById('multiStepTestCase');
    if (multiStepTestCaseSelect && actionData.test_case_id) {
        multiStepTestCaseSelect.value = actionData.test_case_id;
        
        // Trigger change event to populate test case info
        const changeEvent = new Event('change');
        multiStepTestCaseSelect.dispatchEvent(changeEvent);
    }

    // Set the cleanup checkbox
    const multiStepCleanupCheckbox = document.getElementById('multiStepCleanup');
    if (multiStepCleanupCheckbox) {
        multiStepCleanupCheckbox.checked = actionData.cleanup || false;
    }
    
    // ... populate other fields
    break;
```

## Player Logic Compatibility

The existing Android player logic in `app_android/utils/player.py` already correctly handles multiStep actions with `cleanup: true`:

```python
# Check for multiStep actions with cleanup checkbox enabled
if action.get('type') == 'multiStep' and action.get('cleanup', False):
    cleanup_steps.append(action)
    self.logger.info(f"Found cleanup step action (multiStep with cleanup=True): {action.get('test_case_id', 'unknown')}")
```

## Testing

Created comprehensive test script (`test_multistep_cleanup_ui.py`) that verifies:

1. ✅ Cleanup step identification logic works correctly
2. ✅ Action descriptions show cleanup indicators
3. ✅ Sample test cases can be created with cleanup multiStep actions

All tests pass, confirming the solution works as expected.

## Usage

Users can now:

1. **Create multiStep cleanup actions**: Check the "Mark as Cleanup Step" checkbox when creating multiStep actions
2. **Visual identification**: Cleanup multiStep actions show a 🧹 Cleanup badge and [CLEANUP] indicator in descriptions
3. **Edit existing actions**: The cleanup checkbox is properly populated when editing multiStep actions
4. **Execution behavior**: Cleanup multiStep actions are executed during test failures and retries as expected

## Impact

This solution ensures that multiStep actions containing cleanup operations (like app termination, data cleanup, etc.) are properly identified and executed during test failures and retries, maintaining consistency with the iOS framework behavior.
