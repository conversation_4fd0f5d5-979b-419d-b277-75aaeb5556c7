"""
De<PERSON>t for AI Healing Functionality

This script demonstrates how to use the AI healing system with both
real Appium drivers and mock scenarios for testing.
"""

import os
import sys
import time
import logging
from typing import Optional
from appium import webdriver
from appium.options.android import UiAutomator2Options
from appium.options.ios import XCUITestOptions

# Add temp_ai_integration to Python path
sys.path.insert(0, os.path.dirname(__file__))

from together_ai_client import TogetherAIClient
from failure_context_capture import FailureContextCapture, FailureContext
from ui_element_parser import UIElementParser, UIElement
from prompt_builder import PromptBuilder
from ai_healing_orchestrator import AIHealingOrchestrator, HealingResult


def setup_logging():
    """Set up logging for demo"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('ai_healing_demo.log')
        ]
    )


def test_together_ai_connection():
    """Test connection to Together AI API"""
    print("🔍 Testing Together AI Connection...")
    
    try:
        client = TogetherAIClient()
        if client.test_connection():
            print("✅ Together AI connection successful!")
            return True
        else:
            print("❌ Together AI connection failed!")
            return False
    except Exception as e:
        print(f"❌ Together AI connection error: {e}")
        return False


def demo_ui_element_parsing():
    """Demo UI element parsing with sample XML"""
    print("\n🔍 Testing UI Element Parsing...")
    
    # Sample iOS XML
    ios_xml = """<?xml version="1.0" encoding="UTF-8"?>
<XCUIElementTypeApplication type="XCUIElementTypeApplication" name="TestApp" label="TestApp" enabled="true" visible="true" x="0" y="0" width="375" height="812">
    <XCUIElementTypeButton type="XCUIElementTypeButton" name="Login" label="Login" enabled="true" visible="true" x="50" y="300" width="275" height="44"/>
    <XCUIElementTypeTextField type="XCUIElementTypeTextField" name="Username" label="Username" enabled="true" visible="true" x="50" y="200" width="275" height="44"/>
    <XCUIElementTypeSecureTextField type="XCUIElementTypeSecureTextField" name="Password" label="Password" enabled="true" visible="true" x="50" y="250" width="275" height="44"/>
    <XCUIElementTypeStaticText type="XCUIElementTypeStaticText" name="Welcome" label="Welcome" enabled="true" visible="true" x="50" y="100" width="275" height="30"/>
</XCUIElementTypeApplication>"""
    
    # Sample Android XML  
    android_xml = """<?xml version='1.0' encoding='UTF-8' standalone='yes' ?>
<hierarchy rotation="0">
    <android.widget.FrameLayout class="android.widget.FrameLayout" package="com.example.app" bounds="[0,0][1080,1920]" displayed="true" enabled="true">
        <android.widget.Button class="android.widget.Button" text="Login" resource-id="com.example.app:id/login_btn" package="com.example.app" clickable="true" enabled="true" bounds="[100,600][980,700]" displayed="true"/>
        <android.widget.EditText class="android.widget.EditText" text="" hint="Username" resource-id="com.example.app:id/username" package="com.example.app" clickable="true" enabled="true" bounds="[100,400][980,500]" displayed="true"/>
        <android.widget.EditText class="android.widget.EditText" text="" hint="Password" resource-id="com.example.app:id/password" package="com.example.app" clickable="true" enabled="true" bounds="[100,500][980,600]" displayed="true"/>
    </android.widget.FrameLayout>
</hierarchy>"""
    
    parser = UIElementParser()
    
    # Test iOS parsing
    print("  📱 Testing iOS XML parsing...")
    ios_elements = parser.parse_ui_elements(ios_xml, 'iOS')
    print(f"    Found {len(ios_elements)} iOS elements")
    for elem in ios_elements[:3]:
        print(f"    - {elem.element_type}: '{elem.text}' (score: {elem.interaction_score:.2f})")
    
    # Test Android parsing
    print("  🤖 Testing Android XML parsing...")
    android_elements = parser.parse_ui_elements(android_xml, 'Android')
    print(f"    Found {len(android_elements)} Android elements")
    for elem in android_elements[:3]:
        print(f"    - {elem.element_type}: '{elem.text}' (score: {elem.interaction_score:.2f})")
    
    print("✅ UI element parsing demo completed!")


def demo_prompt_building():
    """Demo prompt building functionality"""
    print("\n🔍 Testing Prompt Building...")
    
    # Create mock failure context
    mock_context = FailureContext(
        screenshot_path="/tmp/mock_screenshot.png",
        page_source="<mock>XML content</mock>",
        failed_action="Click on Login button",
        error_message="Element not found: //XCUIElementTypeButton[@name='Login']",
        timestamp=time.time(),
        device_info={'device_name': 'iPhone 14', 'platform_version': '16.0'},
        app_state={'current_activity': 'LoginActivity'},
        window_size=(375, 812),
        platform='iOS'
    )
    
    # Create mock UI elements
    mock_elements = [
        UIElement(
            element_type='XCUIElementTypeButton',
            text='Sign In',
            resource_id='sign_in_btn',
            class_name='XCUIElementTypeButton',
            xpath='//XCUIElementTypeButton[@name="Sign In"]',
            bounds='50,300,325,344',
            clickable=True,
            enabled=True,
            focused=False,
            visible=True,
            content_desc='Sign In Button',
            package='',
            coordinates=(50, 300, 325, 344),
            center_point=(187, 322),
            platform='iOS',
            interaction_score=0.9
        )
    ]
    
    builder = PromptBuilder()
    prompt_context = builder.build_analysis_prompt(mock_context, mock_elements)
    combined_prompt = builder.combine_prompt_components(prompt_context)
    
    print(f"  📝 Generated prompt length: {len(combined_prompt)} characters")
    print(f"  📋 First 200 chars: {combined_prompt[:200]}...")
    print("✅ Prompt building demo completed!")


def demo_mock_healing_session():
    """Demo a complete healing session with mock data"""
    print("\n🔍 Testing Complete Healing Session (Mock)...")
    
    try:
        # Initialize orchestrator
        orchestrator = AIHealingOrchestrator(
            output_dir="demo_outputs",
            max_attempts=2,
            confidence_threshold=0.5
        )
        
        print("  🎭 Creating mock driver...")
        # Note: This is a mock demonstration - in real usage you'd pass a real Appium driver
        
        print("  🩺 Healing session would proceed as follows:")
        print("    1. Capture failure context (screenshot + XML)")
        print("    2. Parse UI elements from page source")
        print("    3. Build optimized prompt for AI analysis")
        print("    4. Send multimodal request to Together AI")
        print("    5. Parse AI response and extract action suggestions")
        print("    6. Execute suggested actions with Appium driver")
        print("    7. Retry up to max_attempts times")
        print("    8. Log results and return healing session")
        
        print("✅ Mock healing session demo completed!")
        
    except Exception as e:
        print(f"❌ Mock healing session failed: {e}")


def demo_real_appium_healing(platform: str = "android"):
    """Demo with real Appium driver (requires device)"""
    print(f"\n🔍 Testing Real Appium Healing ({platform.upper()})...")
    
    driver = None
    try:
        # Set up Appium driver
        if platform.lower() == "android":
            options = UiAutomator2Options()
            options.platform_name = "Android"
            options.device_name = "Android Emulator"  # or your device name
            options.app_package = "com.android.settings"  # Settings app
            options.app_activity = ".Settings"
            options.automation_name = "UiAutomator2"
        else:  # iOS
            options = XCUITestOptions()
            options.platform_name = "iOS"
            options.device_name = "iPhone Simulator"  # or your device name
            options.bundle_id = "com.apple.Preferences"  # Settings app
            options.automation_name = "XCUITest"
        
        # Try to connect to Appium server
        driver = webdriver.Remote("http://127.0.0.1:4723", options=options)
        print("  📱 Connected to Appium driver successfully!")
        
        # Initialize healing orchestrator
        orchestrator = AIHealingOrchestrator(
            output_dir="demo_real_outputs",
            max_attempts=2,
            confidence_threshold=0.6
        )
        
        # Simulate a failure by trying to click a non-existent element
        print("  🧪 Simulating test failure...")
        try:
            driver.find_element("xpath", "//NonExistentElement[@name='DoesNotExist']").click()
        except Exception as original_error:
            print(f"    Original error: {original_error}")
            
            # Trigger AI healing
            print("  🩺 Triggering AI healing...")
            healing_session = orchestrator.heal_test_failure(
                driver=driver,
                failed_action="Click on non-existent element",
                error_message=str(original_error)
            )
            
            print(f"  📊 Healing result: {healing_session.final_result.value}")
            print(f"  ⏱️  Total time: {healing_session.total_time:.2f}s")
            print(f"  🔄 Attempts made: {len(healing_session.attempts)}")
            
            if healing_session.attempts:
                for attempt in healing_session.attempts:
                    print(f"    Attempt {attempt.attempt_number}: {attempt.result.value}")
                    print(f"      Action: {attempt.action_taken}")
                    print(f"      Confidence: {attempt.ai_response.confidence:.2f}")
        
        print("✅ Real Appium healing demo completed!")
        
    except Exception as e:
        print(f"❌ Real Appium demo failed: {e}")
        print(f"   This is expected if no device/emulator is connected or Appium server is not running")
    finally:
        if driver:
            driver.quit()


def run_comprehensive_demo():
    """Run all demo functions"""
    print("🚀 AI Healing Integration Demo")
    print("=" * 50)
    
    # Test API connection first
    if not test_together_ai_connection():
        print("\n❌ Cannot proceed without Together AI connection")
        print("   Please check your TOGETHER_API_KEY in .env file")
        return
    
    # Run component demos
    demo_ui_element_parsing()
    demo_prompt_building()
    demo_mock_healing_session()
    
    # Ask user if they want to test with real Appium
    print("\n" + "=" * 50)
    print("🤖 Real Appium Testing")
    print("This requires a running Appium server and connected device/emulator")
    
    response = input("Test with real Appium? (y/N): ").strip().lower()
    if response in ['y', 'yes']:
        platform = input("Platform (android/ios) [android]: ").strip().lower() or 'android'
        demo_real_appium_healing(platform)
    else:
        print("Skipping real Appium demo")
    
    print("\n" + "=" * 50)
    print("🎉 Demo completed! Check ai_healing_demo.log for detailed logs")


if __name__ == "__main__":
    setup_logging()
    
    # Check if Together AI key is configured
    if not os.getenv('TOGETHER_API_KEY'):
        print("❌ TOGETHER_API_KEY not found in environment variables")
        print("   Please set your Together AI API key in the .env file")
        sys.exit(1)
    
    try:
        run_comprehensive_demo()
    except KeyboardInterrupt:
        print("\n\n👋 Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed with error: {e}")
        logging.exception("Demo failed")
        sys.exit(1)