"""
UI Element Parser Module

Parses XML page source from iOS and Android apps to extract actionable
UI elements with their locators, attributes, and interaction capabilities.
"""

import xml.etree.ElementTree as ET
import logging
import re
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass


@dataclass
class UIElement:
    """Represents a parsed UI element with interaction capabilities"""
    element_type: str
    text: str
    resource_id: str
    class_name: str
    xpath: str
    bounds: str
    clickable: bool
    enabled: bool
    focused: bool
    visible: bool
    content_desc: str
    package: str
    coordinates: Tuple[int, int, int, int]  # x1, y1, x2, y2
    center_point: Tuple[int, int]
    platform: str  # 'iOS' or 'Android'
    interaction_score: float  # Likelihood this element is useful for interaction


class UIElementParser:
    """Parses UI hierarchies to extract actionable elements"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Interaction-worthy element types
        self.interactive_types = {
            'android': {
                'android.widget.Button',
                'android.widget.ImageButton',
                'android.widget.EditText',
                'android.widget.TextView',
                'android.widget.CheckBox',
                'android.widget.RadioButton',
                'android.widget.Switch',
                'android.widget.ToggleButton',
                'android.widget.Spinner',
                'android.widget.ImageView',
                'android.view.View',
                'android.widget.FrameLayout',
                'android.widget.LinearLayout',
                'android.widget.RelativeLayout'
            },
            'ios': {
                'XCUIElementTypeButton',
                'XCUIElementTypeTextField',
                'XCUIElementTypeSecureTextField', 
                'XCUIElementTypeStaticText',
                'XCUIElementTypeCell',
                'XCUIElementTypeImage',
                'XCUIElementTypeSwitch',
                'XCUIElementTypeSlider',
                'XCUIElementTypeSegmentedControl',
                'XCUIElementTypePicker',
                'XCUIElementTypePickerWheel',
                'XCUIElementTypeSearchField',
                'XCUIElementTypeOther'
            }
        }
    
    def parse_ui_elements(self, page_source: str, platform: str = 'Unknown') -> List[UIElement]:
        """
        Parse UI elements from page source XML
        
        Args:
            page_source: XML page source from Appium
            platform: 'iOS', 'Android', or 'Unknown'
            
        Returns:
            List of parsed UIElement objects sorted by interaction score
        """
        try:
            if not page_source or not page_source.strip():
                self.logger.warning("Empty page source provided")
                return []
            
            # Parse XML
            root = ET.fromstring(page_source)
            
            # Auto-detect platform if not specified
            if platform == 'Unknown':
                platform = self._detect_platform(root)
            
            # Extract elements
            elements = []
            self._extract_elements_recursive(root, elements, platform)
            
            # Calculate interaction scores and filter
            scored_elements = []
            for element in elements:
                score = self._calculate_interaction_score(element)
                if score > 0.1:  # Only include elements with meaningful interaction potential
                    element.interaction_score = score
                    scored_elements.append(element)
            
            # Sort by interaction score (descending)
            scored_elements.sort(key=lambda x: x.interaction_score, reverse=True)
            
            self.logger.info(f"Parsed {len(scored_elements)} actionable UI elements from {platform}")
            return scored_elements
            
        except ET.ParseError as e:
            self.logger.error(f"XML parsing error: {e}")
            return []
        except Exception as e:
            self.logger.error(f"Unexpected error parsing UI elements: {e}")
            return []
    
    def _detect_platform(self, root: ET.Element) -> str:
        """Auto-detect platform from XML structure"""
        # Check for iOS-specific attributes
        if any('XCUIElementType' in elem.tag for elem in root.iter()):
            return 'iOS'
        
        # Check for Android-specific attributes
        if any('android.widget' in elem.tag or 'android.view' in elem.tag for elem in root.iter()):
            return 'Android'
        
        # Check attributes
        for elem in root.iter():
            if 'package' in elem.attrib or 'resource-id' in elem.attrib:
                return 'Android'
            if 'name' in elem.attrib and 'type' in elem.attrib:
                return 'iOS'
        
        return 'Unknown'
    
    def _extract_elements_recursive(self, element: ET.Element, elements: List[UIElement], platform: str, xpath_prefix: str = ""):
        """Recursively extract UI elements from XML tree"""
        try:
            # Build XPath for current element
            tag_name = element.tag
            xpath = f"{xpath_prefix}/{tag_name}"
            
            # Add position predicate if there are siblings with same tag
            siblings = [child for child in element.getparent().iter(tag_name)] if element.getparent() is not None else [element]
            if len(siblings) > 1:
                position = siblings.index(element) + 1
                xpath += f"[{position}]"
            
            # Extract element attributes based on platform
            ui_element = self._create_ui_element(element, platform, xpath)
            
            # Only add if it's potentially interactive
            if self._is_potentially_interactive(ui_element, platform):
                elements.append(ui_element)
            
            # Recursively process children
            for child in element:
                self._extract_elements_recursive(child, elements, platform, xpath)
                
        except Exception as e:
            self.logger.debug(f"Error processing element {element.tag}: {e}")
    
    def _create_ui_element(self, element: ET.Element, platform: str, xpath: str) -> UIElement:
        """Create UIElement object from XML element"""
        attrs = element.attrib
        
        if platform == 'Android':
            return self._create_android_element(attrs, xpath)
        elif platform == 'iOS':
            return self._create_ios_element(attrs, xpath)
        else:
            return self._create_generic_element(attrs, xpath, platform)
    
    def _create_android_element(self, attrs: Dict[str, str], xpath: str) -> UIElement:
        """Create UIElement for Android platform"""
        # Parse bounds for coordinates
        bounds_str = attrs.get('bounds', '[0,0][0,0]')
        coordinates = self._parse_android_bounds(bounds_str)
        center_point = self._calculate_center(coordinates)
        
        return UIElement(
            element_type=attrs.get('class', 'Unknown'),
            text=attrs.get('text', ''),
            resource_id=attrs.get('resource-id', ''),
            class_name=attrs.get('class', ''),
            xpath=xpath,
            bounds=bounds_str,
            clickable=attrs.get('clickable', 'false').lower() == 'true',
            enabled=attrs.get('enabled', 'false').lower() == 'true',
            focused=attrs.get('focused', 'false').lower() == 'true',
            visible=attrs.get('displayed', 'false').lower() == 'true',
            content_desc=attrs.get('content-desc', ''),
            package=attrs.get('package', ''),
            coordinates=coordinates,
            center_point=center_point,
            platform='Android',
            interaction_score=0.0
        )
    
    def _create_ios_element(self, attrs: Dict[str, str], xpath: str) -> UIElement:
        """Create UIElement for iOS platform"""
        # Parse bounds for coordinates
        bounds_str = attrs.get('x', '0') + ',' + attrs.get('y', '0') + ',' + attrs.get('width', '0') + ',' + attrs.get('height', '0')
        coordinates = self._parse_ios_bounds(attrs)
        center_point = self._calculate_center(coordinates)
        
        return UIElement(
            element_type=attrs.get('type', 'Unknown'),
            text=attrs.get('name', '') or attrs.get('value', ''),
            resource_id=attrs.get('name', ''),
            class_name=attrs.get('type', ''),
            xpath=xpath,
            bounds=bounds_str,
            clickable=attrs.get('enabled', 'false').lower() == 'true',
            enabled=attrs.get('enabled', 'false').lower() == 'true',
            focused=attrs.get('focused', 'false').lower() == 'true',
            visible=attrs.get('visible', 'false').lower() == 'true',
            content_desc=attrs.get('label', ''),
            package='',
            coordinates=coordinates,
            center_point=center_point,
            platform='iOS',
            interaction_score=0.0
        )
    
    def _create_generic_element(self, attrs: Dict[str, str], xpath: str, platform: str) -> UIElement:
        """Create UIElement for unknown platform"""
        return UIElement(
            element_type=attrs.get('type', attrs.get('class', 'Unknown')),
            text=attrs.get('text', attrs.get('name', attrs.get('value', ''))),
            resource_id=attrs.get('resource-id', attrs.get('name', '')),
            class_name=attrs.get('class', attrs.get('type', '')),
            xpath=xpath,
            bounds=attrs.get('bounds', ''),
            clickable=True,  # Assume clickable for unknown platforms
            enabled=True,
            focused=False,
            visible=True,
            content_desc=attrs.get('content-desc', attrs.get('label', '')),
            package=attrs.get('package', ''),
            coordinates=(0, 0, 0, 0),
            center_point=(0, 0),
            platform=platform,
            interaction_score=0.0
        )
    
    def _parse_android_bounds(self, bounds_str: str) -> Tuple[int, int, int, int]:
        """Parse Android bounds string like '[x1,y1][x2,y2]'"""
        try:
            match = re.match(r'\[(\d+),(\d+)\]\[(\d+),(\d+)\]', bounds_str)
            if match:
                return tuple(map(int, match.groups()))
            return (0, 0, 0, 0)
        except:
            return (0, 0, 0, 0)
    
    def _parse_ios_bounds(self, attrs: Dict[str, str]) -> Tuple[int, int, int, int]:
        """Parse iOS bounds from x, y, width, height attributes"""
        try:
            x = int(attrs.get('x', '0'))
            y = int(attrs.get('y', '0'))
            width = int(attrs.get('width', '0'))
            height = int(attrs.get('height', '0'))
            return (x, y, x + width, y + height)
        except:
            return (0, 0, 0, 0)
    
    def _calculate_center(self, coordinates: Tuple[int, int, int, int]) -> Tuple[int, int]:
        """Calculate center point from coordinates"""
        x1, y1, x2, y2 = coordinates
        return ((x1 + x2) // 2, (y1 + y2) // 2)
    
    def _is_potentially_interactive(self, element: UIElement, platform: str) -> bool:
        """Check if element is potentially interactive"""
        platform_key = platform.lower()
        
        # Check if element type is in interactive types
        if platform_key in self.interactive_types:
            if element.element_type in self.interactive_types[platform_key]:
                return True
        
        # Additional heuristics
        if element.clickable:
            return True
        
        if element.text and len(element.text.strip()) > 0:
            return True
        
        if element.content_desc and len(element.content_desc.strip()) > 0:
            return True
        
        return False
    
    def _calculate_interaction_score(self, element: UIElement) -> float:
        """Calculate interaction score for element prioritization"""
        score = 0.0
        
        # Base score for clickable elements
        if element.clickable:
            score += 0.4
        
        # Boost for enabled elements
        if element.enabled:
            score += 0.2
        
        # Boost for visible elements
        if element.visible:
            score += 0.2
        
        # Text content boosts
        if element.text:
            text_len = len(element.text.strip())
            if text_len > 0:
                score += min(0.3, text_len * 0.01)  # Up to 0.3 boost for text
        
        # Content description boost
        if element.content_desc:
            score += 0.1
        
        # Element type specific scores
        type_scores = {
            # High priority elements
            'android.widget.Button': 0.8,
            'android.widget.ImageButton': 0.7,
            'android.widget.EditText': 0.9,
            'XCUIElementTypeButton': 0.8,
            'XCUIElementTypeTextField': 0.9,
            'XCUIElementTypeSecureTextField': 0.9,
            
            # Medium priority elements
            'android.widget.TextView': 0.5,
            'android.widget.ImageView': 0.4,
            'XCUIElementTypeStaticText': 0.5,
            'XCUIElementTypeImage': 0.4,
            
            # Low priority elements
            'android.view.View': 0.2,
            'XCUIElementTypeOther': 0.2,
        }
        
        if element.element_type in type_scores:
            score += type_scores[element.element_type]
        
        # Resource ID boost (better for automation)
        if element.resource_id:
            score += 0.2
        
        # Size-based scoring (larger elements more likely to be interactive)
        x1, y1, x2, y2 = element.coordinates
        area = (x2 - x1) * (y2 - y1)
        if area > 100:  # Reasonable size
            score += min(0.2, area / 10000)  # Up to 0.2 boost for larger elements
        
        return min(1.0, score)  # Cap at 1.0
    
    def filter_elements_by_criteria(self, 
                                   elements: List[UIElement],
                                   min_score: float = 0.3,
                                   max_elements: int = 20,
                                   include_text_filter: Optional[str] = None,
                                   exclude_types: Optional[List[str]] = None) -> List[UIElement]:
        """Filter elements by various criteria"""
        filtered = elements
        
        # Score filter
        filtered = [e for e in filtered if e.interaction_score >= min_score]
        
        # Text filter
        if include_text_filter:
            text_filter = include_text_filter.lower()
            filtered = [e for e in filtered if 
                       text_filter in e.text.lower() or 
                       text_filter in e.content_desc.lower()]
        
        # Type exclusion filter
        if exclude_types:
            filtered = [e for e in filtered if e.element_type not in exclude_types]
        
        # Limit number
        filtered = filtered[:max_elements]
        
        return filtered
    
    def generate_alternative_xpaths(self, element: UIElement) -> List[str]:
        """Generate alternative XPath expressions for an element"""
        xpaths = [element.xpath]  # Original xpath
        
        # By text content
        if element.text:
            text_escaped = element.text.replace("'", "\\'")
            if element.platform == 'Android':
                xpaths.append(f"//*[@text='{text_escaped}']")
            elif element.platform == 'iOS':
                xpaths.append(f"//*[@name='{text_escaped}']")
        
        # By resource ID / name
        if element.resource_id:
            id_escaped = element.resource_id.replace("'", "\\'")
            if element.platform == 'Android':
                xpaths.append(f"//*[@resource-id='{id_escaped}']")
            elif element.platform == 'iOS':
                xpaths.append(f"//*[@name='{id_escaped}']")
        
        # By content description / label
        if element.content_desc:
            desc_escaped = element.content_desc.replace("'", "\\'")
            if element.platform == 'Android':
                xpaths.append(f"//*[@content-desc='{desc_escaped}']")
            elif element.platform == 'iOS':
                xpaths.append(f"//*[@label='{desc_escaped}']")
        
        # By class/type
        if element.element_type:
            xpaths.append(f"//{element.element_type}")
        
        # Combined selectors
        if element.text and element.element_type:
            text_escaped = element.text.replace("'", "\\'")
            if element.platform == 'Android':
                xpaths.append(f"//{element.element_type}[@text='{text_escaped}']")
            elif element.platform == 'iOS':
                xpaths.append(f"//{element.element_type}[@name='{text_escaped}']")
        
        return list(set(xpaths))  # Remove duplicates