"""
AI Healing Orchestrator Module

Main orchestrator that coordinates AI-powered test healing by combining
failure context capture, UI analysis, LLM communication, and action execution.
"""

import os
import time
import logging
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass
from enum import Enum
from appium.webdriver.webdriver import WebDriver
from selenium.webdriver.common.by import By
from selenium.common.exceptions import WebDriverException, TimeoutException, NoSuchElementException
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

from .together_ai_client import TogetherAIClient, AIResponse
from .failure_context_capture import FailureContextCapture, FailureContext
from .ui_element_parser import UIElementParser, UIElement
from .prompt_builder import PromptBuilder, PromptContext


class HealingResult(Enum):
    """Possible outcomes of healing attempts"""
    SUCCESS = "success"
    FAILED = "failed"
    RETRY_NEEDED = "retry_needed"
    MAX_ATTEMPTS_REACHED = "max_attempts"
    DISABLED = "disabled"


@dataclass
class HealingAttempt:
    """Record of a single healing attempt"""
    attempt_number: int
    ai_response: AIResponse
    action_taken: str
    result: HealingResult
    error_message: Optional[str]
    execution_time: float
    screenshot_path: Optional[str]


@dataclass
class HealingSession:
    """Complete healing session with all attempts"""
    session_id: str
    original_action: str
    original_error: str
    platform: str
    attempts: List[HealingAttempt]
    final_result: HealingResult
    total_time: float
    success_action: Optional[str] = None


class AIHealingOrchestrator:
    """Main orchestrator for AI-powered test healing"""
    
    def __init__(self, 
                 together_api_key: Optional[str] = None,
                 output_dir: str = "ai_healing_outputs",
                 max_attempts: int = 3,
                 confidence_threshold: float = 0.6):
        """
        Initialize AI healing orchestrator
        
        Args:
            together_api_key: Together AI API key
            output_dir: Directory for healing outputs and logs
            max_attempts: Maximum healing attempts per failure
            confidence_threshold: Minimum confidence for healing suggestions
        """
        self.logger = logging.getLogger(__name__)
        
        # Configuration
        self.max_attempts = int(os.getenv('AI_HEALING_MAX_ATTEMPTS', str(max_attempts)))
        self.confidence_threshold = float(os.getenv('AI_HEALING_CONFIDENCE_THRESHOLD', str(confidence_threshold)))
        self.enabled = os.getenv('AI_HEALING_ENABLED', 'true').lower() == 'true'
        
        if not self.enabled:
            self.logger.info("AI healing is disabled via configuration")
            return
        
        # Initialize components
        try:
            self.ai_client = TogetherAIClient(api_key=together_api_key)
            self.context_capture = FailureContextCapture(output_dir=f"{output_dir}/contexts")
            self.ui_parser = UIElementParser()
            self.prompt_builder = PromptBuilder()
            
            # Test connection
            if not self.ai_client.test_connection():
                raise Exception("Failed to connect to Together AI API")
                
            self.logger.info("AI healing orchestrator initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize AI healing: {e}")
            self.enabled = False
            raise
    
    def heal_test_failure(self,
                         driver: WebDriver,
                         failed_action: str,
                         error_message: str,
                         retry_original_action: Optional[Callable] = None) -> HealingSession:
        """
        Main entry point for healing test failures
        
        Args:
            driver: Appium WebDriver instance
            failed_action: Description of the action that failed
            error_message: Error message from the test failure
            retry_original_action: Optional callback to retry the original action
            
        Returns:
            HealingSession with results of all healing attempts
        """
        session_start_time = time.time()
        session_id = f"healing_{int(session_start_time)}"
        
        self.logger.info(f"Starting healing session {session_id} for: {failed_action}")
        
        if not self.enabled:
            return HealingSession(
                session_id=session_id,
                original_action=failed_action,
                original_error=error_message,
                platform="Unknown",
                attempts=[],
                final_result=HealingResult.DISABLED,
                total_time=0.0
            )
        
        attempts = []
        healing_result = HealingResult.FAILED
        
        try:
            # Capture initial failure context
            failure_context = self.context_capture.capture_failure_context(
                driver, failed_action, error_message
            )
            
            # Parse UI elements
            ui_elements = self.ui_parser.parse_ui_elements(
                failure_context.page_source, failure_context.platform
            )
            
            self.logger.info(f"Parsed {len(ui_elements)} UI elements for analysis")
            
            # Attempt healing with AI suggestions
            for attempt_num in range(1, self.max_attempts + 1):
                self.logger.info(f"Healing attempt {attempt_num}/{self.max_attempts}")
                
                attempt = self._execute_healing_attempt(
                    driver, failure_context, ui_elements, attempt_num, attempts
                )
                attempts.append(attempt)
                
                if attempt.result == HealingResult.SUCCESS:
                    healing_result = HealingResult.SUCCESS
                    self.logger.info(f"Healing successful on attempt {attempt_num}")
                    break
                elif attempt.result == HealingResult.RETRY_NEEDED:
                    # Continue to next attempt
                    continue
                else:
                    # Failed attempt, but continue trying
                    continue
            
            if healing_result != HealingResult.SUCCESS and len(attempts) >= self.max_attempts:
                healing_result = HealingResult.MAX_ATTEMPTS_REACHED
            
            # Try original action if healing provided insights
            if healing_result != HealingResult.SUCCESS and retry_original_action:
                try:
                    retry_original_action()
                    healing_result = HealingResult.SUCCESS
                    self.logger.info("Original action succeeded after AI analysis")
                except Exception as e:
                    self.logger.debug(f"Original action still fails: {e}")
            
        except Exception as e:
            self.logger.error(f"Healing session failed: {e}")
            healing_result = HealingResult.FAILED
        
        total_time = time.time() - session_start_time
        
        session = HealingSession(
            session_id=session_id,
            original_action=failed_action,
            original_error=error_message,
            platform=failure_context.platform if 'failure_context' in locals() else "Unknown",
            attempts=attempts,
            final_result=healing_result,
            total_time=total_time,
            success_action=attempts[-1].action_taken if attempts and healing_result == HealingResult.SUCCESS else None
        )
        
        self._log_healing_session(session)
        return session
    
    def _execute_healing_attempt(self,
                                driver: WebDriver,
                                failure_context: FailureContext,
                                ui_elements: List[UIElement],
                                attempt_num: int,
                                previous_attempts: List[HealingAttempt]) -> HealingAttempt:
        """Execute a single healing attempt"""
        attempt_start_time = time.time()
        
        try:
            # Build prompt context
            prompt_context = self.prompt_builder.build_analysis_prompt(
                failure_context, ui_elements
            )
            
            # Modify prompt for follow-up attempts
            if previous_attempts:
                last_attempt = previous_attempts[-1]
                follow_up_prompt = self.prompt_builder.build_follow_up_prompt(
                    last_attempt.action_taken,
                    last_attempt.error_message or "Unknown error",
                    attempt_num
                )
                prompt_context.recovery_guidance = follow_up_prompt
            
            # Get AI analysis
            combined_prompt = self.prompt_builder.combine_prompt_components(prompt_context)
            
            ai_response = self.ai_client.analyze_failure_context(
                screenshot_path=failure_context.screenshot_path,
                ui_elements=[self._ui_element_to_dict(elem) for elem in ui_elements],
                failed_action=failure_context.failed_action,
                error_message=failure_context.error_message,
                additional_context=combined_prompt
            )
            
            self.logger.info(f"AI suggestion (confidence {ai_response.confidence:.2f}): {ai_response.suggested_action}")
            
            # Check confidence threshold
            if ai_response.confidence < self.confidence_threshold:
                self.logger.warning(f"AI confidence {ai_response.confidence:.2f} below threshold {self.confidence_threshold}")
                return HealingAttempt(
                    attempt_number=attempt_num,
                    ai_response=ai_response,
                    action_taken="Skipped due to low confidence",
                    result=HealingResult.RETRY_NEEDED,
                    error_message=f"Confidence {ai_response.confidence:.2f} below threshold",
                    execution_time=time.time() - attempt_start_time,
                    screenshot_path=None
                )
            
            # Execute suggested action
            execution_result = self._execute_suggested_action(driver, ai_response)
            
            return HealingAttempt(
                attempt_number=attempt_num,
                ai_response=ai_response,
                action_taken=ai_response.suggested_action,
                result=execution_result['result'],
                error_message=execution_result.get('error'),
                execution_time=time.time() - attempt_start_time,
                screenshot_path=execution_result.get('screenshot')
            )
            
        except Exception as e:
            self.logger.error(f"Healing attempt {attempt_num} failed: {e}")
            return HealingAttempt(
                attempt_number=attempt_num,
                ai_response=AIResponse("", 0.0, str(e), [], "error"),
                action_taken="Failed to execute",
                result=HealingResult.FAILED,
                error_message=str(e),
                execution_time=time.time() - attempt_start_time,
                screenshot_path=None
            )
    
    def _execute_suggested_action(self, driver: WebDriver, ai_response: AIResponse) -> Dict[str, Any]:
        """Execute the action suggested by AI"""
        result = {'result': HealingResult.FAILED}
        
        try:
            action_type = ai_response.action_type.lower()
            xpaths = ai_response.xpath_suggestions
            
            if not xpaths:
                result['error'] = "No XPath suggestions provided"
                return result
            
            # Try each suggested XPath
            for xpath in xpaths:
                try:
                    self.logger.debug(f"Trying XPath: {xpath}")
                    element = driver.find_element(By.XPATH, xpath)
                    
                    if action_type == 'click':
                        element.click()
                    elif action_type == 'type':
                        # Extract text to type from suggested action
                        text_to_type = self._extract_text_from_action(ai_response.suggested_action)
                        if text_to_type:
                            element.clear()
                            element.send_keys(text_to_type)
                        else:
                            result['error'] = "No text specified for typing action"
                            continue
                    elif action_type == 'double_tap':
                        # Perform double tap using TouchAction
                        from appium.webdriver.common.touch_action import TouchAction
                        action = TouchAction(driver)
                        action.tap(element).tap(element).perform()
                    elif action_type == 'long_press':
                        from appium.webdriver.common.touch_action import TouchAction
                        action = TouchAction(driver)
                        action.long_press(element).release().perform()
                    elif action_type == 'swipe':
                        # Perform swipe based on suggested direction
                        self._perform_swipe_action(driver, ai_response.suggested_action)
                    elif action_type == 'wait':
                        # Wait for element to become available
                        wait_time = self._extract_wait_time(ai_response.suggested_action)
                        WebDriverWait(driver, wait_time).until(
                            EC.element_to_be_clickable((By.XPATH, xpath))
                        )
                    else:
                        # Default to click
                        element.click()
                    
                    # Action succeeded
                    result['result'] = HealingResult.SUCCESS
                    result['xpath_used'] = xpath
                    self.logger.info(f"Successfully executed {action_type} on {xpath}")
                    break
                    
                except (NoSuchElementException, TimeoutException):
                    self.logger.debug(f"Element not found with XPath: {xpath}")
                    continue
                except WebDriverException as e:
                    self.logger.debug(f"WebDriver error with XPath {xpath}: {e}")
                    continue
            
            if result['result'] != HealingResult.SUCCESS:
                result['error'] = "All XPath suggestions failed"
                result['result'] = HealingResult.RETRY_NEEDED
            
        except Exception as e:
            result['error'] = str(e)
            result['result'] = HealingResult.FAILED
            self.logger.error(f"Error executing suggested action: {e}")
        
        return result
    
    def _extract_text_from_action(self, action_description: str) -> Optional[str]:
        """Extract text to type from action description"""
        import re
        
        # Look for patterns like "type 'text'" or "enter 'text'"
        patterns = [
            r"type[s]?\s+['\"]([^'\"]+)['\"]",
            r"enter[s]?\s+['\"]([^'\"]+)['\"]",
            r"input[s]?\s+['\"]([^'\"]+)['\"]"
        ]
        
        for pattern in patterns:
            match = re.search(pattern, action_description, re.IGNORECASE)
            if match:
                return match.group(1)
        
        return None
    
    def _extract_wait_time(self, action_description: str) -> int:
        """Extract wait time from action description"""
        import re
        
        # Look for patterns like "wait 5 seconds" or "wait for 10"
        patterns = [
            r"wait\s+(\d+)\s+seconds?",
            r"wait\s+for\s+(\d+)",
            r"(\d+)\s+seconds?"
        ]
        
        for pattern in patterns:
            match = re.search(pattern, action_description, re.IGNORECASE)
            if match:
                return int(match.group(1))
        
        return 5  # Default wait time
    
    def _perform_swipe_action(self, driver: WebDriver, action_description: str):
        """Perform swipe action based on description"""
        from appium.webdriver.common.touch_action import TouchAction
        
        # Get screen size
        size = driver.get_window_size()
        width = size['width']
        height = size['height']
        
        # Determine swipe direction
        direction = 'down'  # default
        if 'up' in action_description.lower():
            direction = 'up'
        elif 'left' in action_description.lower():
            direction = 'left'
        elif 'right' in action_description.lower():
            direction = 'right'
        
        # Calculate swipe coordinates
        center_x = width // 2
        center_y = height // 2
        
        if direction == 'up':
            start = (center_x, center_y + 200)
            end = (center_x, center_y - 200)
        elif direction == 'down':
            start = (center_x, center_y - 200)
            end = (center_x, center_y + 200)
        elif direction == 'left':
            start = (center_x + 200, center_y)
            end = (center_x - 200, center_y)
        else:  # right
            start = (center_x - 200, center_y)
            end = (center_x + 200, center_y)
        
        # Perform swipe
        action = TouchAction(driver)
        action.press(x=start[0], y=start[1]).move_to(x=end[0], y=end[1]).release().perform()
        
        self.logger.info(f"Performed {direction} swipe from {start} to {end}")
    
    def _ui_element_to_dict(self, element: UIElement) -> Dict[str, Any]:
        """Convert UIElement to dictionary for AI client"""
        return {
            'type': element.element_type,
            'text': element.text,
            'xpath': element.xpath,
            'bounds': element.bounds,
            'clickable': element.clickable,
            'enabled': element.enabled,
            'visible': element.visible,
            'content_desc': element.content_desc,
            'coordinates': element.coordinates,
            'center_point': element.center_point,
            'interaction_score': element.interaction_score
        }
    
    def _log_healing_session(self, session: HealingSession):
        """Log healing session results"""
        self.logger.info(f"Healing session {session.session_id} completed:")
        self.logger.info(f"  Result: {session.final_result.value}")
        self.logger.info(f"  Attempts: {len(session.attempts)}")
        self.logger.info(f"  Total time: {session.total_time:.2f}s")
        
        if session.final_result == HealingResult.SUCCESS and session.success_action:
            self.logger.info(f"  Success action: {session.success_action}")
        
        # Log each attempt
        for attempt in session.attempts:
            self.logger.debug(
                f"  Attempt {attempt.attempt_number}: "
                f"{attempt.result.value} - {attempt.action_taken} "
                f"(confidence: {attempt.ai_response.confidence:.2f})"
            )
    
    def get_healing_statistics(self) -> Dict[str, Any]:
        """Get healing performance statistics"""
        # This would typically read from a persistent store
        # For now, return basic configuration info
        return {
            'enabled': self.enabled,
            'max_attempts': self.max_attempts,
            'confidence_threshold': self.confidence_threshold,
            'components_initialized': all([
                hasattr(self, 'ai_client'),
                hasattr(self, 'context_capture'),
                hasattr(self, 'ui_parser'),
                hasattr(self, 'prompt_builder')
            ])
        }