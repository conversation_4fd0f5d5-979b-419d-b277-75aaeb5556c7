"""
Prompt Builder Module

Creates optimized prompts for multimodal LLM analysis, combining visual context
with structured UI element information for effective test healing suggestions.
"""

import logging
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from .ui_element_parser import UIElement
from .failure_context_capture import FailureContext


@dataclass 
class PromptContext:
    """Container for all prompt context data"""
    visual_prompt: str
    structured_elements: str
    failure_summary: str
    platform_context: str
    recovery_guidance: str


class PromptBuilder:
    """Builds optimized prompts for multimodal LLM analysis"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Platform-specific guidance
        self.platform_guidance = {
            'iOS': {
                'locator_preferences': ['name', 'label', 'type'],
                'interaction_patterns': ['tap', 'double_tap', 'long_press', 'swipe'],
                'common_issues': [
                    'WebDriverAgent connection issues',
                    'Element timing issues',
                    'Keyboard dismissal needed',
                    'App permissions required'
                ]
            },
            'Android': {
                'locator_preferences': ['resource-id', 'text', 'content-desc', 'class'],
                'interaction_patterns': ['click', 'long_click', 'swipe', 'scroll'],
                'common_issues': [
                    'UiAutomator2 driver issues',
                    'Soft keyboard interference',
                    'Animation timing',
                    'Activity context changes'
                ]
            }
        }
    
    def build_analysis_prompt(self,
                             failure_context: FailureContext,
                             ui_elements: List[UIElement],
                             max_elements: int = 15,
                             include_recovery_examples: bool = True) -> PromptContext:
        """
        Build comprehensive analysis prompt for multimodal LLM
        
        Args:
            failure_context: Complete failure context information
            ui_elements: Parsed UI elements from the screen
            max_elements: Maximum elements to include in prompt
            include_recovery_examples: Whether to include recovery examples
            
        Returns:
            PromptContext with structured prompt components
        """
        try:
            # Filter and prioritize elements
            prioritized_elements = self._prioritize_elements(ui_elements, max_elements)
            
            # Build prompt components
            visual_prompt = self._build_visual_analysis_prompt()
            structured_elements = self._build_elements_description(prioritized_elements)
            failure_summary = self._build_failure_summary(failure_context)
            platform_context = self._build_platform_context(failure_context.platform)
            recovery_guidance = self._build_recovery_guidance(failure_context.platform, include_recovery_examples)
            
            return PromptContext(
                visual_prompt=visual_prompt,
                structured_elements=structured_elements,
                failure_summary=failure_summary,
                platform_context=platform_context,
                recovery_guidance=recovery_guidance
            )
            
        except Exception as e:
            self.logger.error(f"Failed to build analysis prompt: {e}")
            raise
    
    def combine_prompt_components(self, prompt_context: PromptContext) -> str:
        """Combine all prompt components into final prompt string"""
        
        prompt = f"""MOBILE APP AUTOMATION FAILURE ANALYSIS

{prompt_context.failure_summary}

{prompt_context.platform_context}

{prompt_context.structured_elements}

{prompt_context.visual_prompt}

{prompt_context.recovery_guidance}

RESPONSE FORMAT:
Please respond with a JSON object containing your analysis and recommendations:

{{
    "analysis": {{
        "issue_identified": "Brief description of what went wrong",
        "visual_observations": "What you see in the screenshot",
        "likely_cause": "Most probable cause of the failure"
    }},
    "suggested_action": "Primary recommended action (e.g., 'Click on Login button')",
    "confidence": 0.85,
    "reasoning": "Detailed explanation of why this action should work",
    "xpath_suggestions": [
        "//XCUIElementTypeButton[@name='Login']",
        "//*[@text='Login']"
    ],
    "action_type": "click",
    "alternative_strategies": [
        {{
            "action": "Wait for element to appear",
            "xpath": "//XCUIElementTypeButton[@name='Login']",
            "wait_time": 5,
            "confidence": 0.7
        }},
        {{
            "action": "Scroll to find element",
            "direction": "down",
            "confidence": 0.6
        }}
    ],
    "platform_specific_notes": "Any iOS/Android specific considerations"
}}

Focus on providing actionable, specific recommendations that can be immediately implemented in Appium test automation."""

        return prompt
    
    def _prioritize_elements(self, ui_elements: List[UIElement], max_elements: int) -> List[UIElement]:
        """Prioritize UI elements for inclusion in prompt"""
        # Sort by interaction score and take top elements
        sorted_elements = sorted(ui_elements, key=lambda x: x.interaction_score, reverse=True)
        
        # Ensure we have a good mix of element types
        prioritized = []
        button_count = 0
        text_count = 0
        input_count = 0
        
        for element in sorted_elements:
            if len(prioritized) >= max_elements:
                break
                
            # Limit certain types to avoid prompt clutter
            element_type_lower = element.element_type.lower()
            
            if 'button' in element_type_lower:
                if button_count < 5:  # Max 5 buttons
                    prioritized.append(element)
                    button_count += 1
            elif 'text' in element_type_lower and 'edit' not in element_type_lower:
                if text_count < 3:  # Max 3 text elements
                    prioritized.append(element)
                    text_count += 1
            elif any(t in element_type_lower for t in ['edit', 'field', 'input']):
                if input_count < 3:  # Max 3 input elements
                    prioritized.append(element)
                    input_count += 1
            else:
                prioritized.append(element)
        
        return prioritized
    
    def _build_visual_analysis_prompt(self) -> str:
        """Build visual analysis component of prompt"""
        return """VISUAL ANALYSIS REQUIRED:
Please examine the provided screenshot carefully and identify:
1. The current state of the mobile application
2. Visible UI elements and their positions
3. Any error messages, dialogs, or unexpected states
4. Elements that might be clickable but not properly identified in the XML
5. Visual clues about what the user was trying to accomplish
6. Any overlays, modals, or pop-ups that might be blocking interactions"""
    
    def _build_elements_description(self, ui_elements: List[UIElement]) -> str:
        """Build structured description of UI elements"""
        if not ui_elements:
            return "AVAILABLE UI ELEMENTS: None detected (this might indicate an app state issue)"
        
        elements_desc = "AVAILABLE UI ELEMENTS:\n"
        
        for i, element in enumerate(ui_elements, 1):
            # Build element description
            desc_parts = []
            
            # Element type and text
            if element.text:
                desc_parts.append(f"Text: '{element.text}'")
            if element.content_desc:
                desc_parts.append(f"Description: '{element.content_desc}'")
            
            # Interaction capabilities
            capabilities = []
            if element.clickable:
                capabilities.append("clickable")
            if element.enabled:
                capabilities.append("enabled")
            if element.visible:
                capabilities.append("visible")
            
            capability_str = f"[{', '.join(capabilities)}]" if capabilities else "[not interactive]"
            
            # Coordinates for position reference
            x1, y1, x2, y2 = element.coordinates
            position = f"Position: ({element.center_point[0]}, {element.center_point[1]})"
            
            # Primary XPath
            xpath = element.xpath
            
            # Build full description
            desc = f"{i}. {element.element_type} {capability_str}"
            if desc_parts:
                desc += f" - {' | '.join(desc_parts)}"
            desc += f"\n   XPath: {xpath}\n   {position}\n   Score: {element.interaction_score:.2f}"
            
            elements_desc += desc + "\n\n"
        
        return elements_desc.strip()
    
    def _build_failure_summary(self, failure_context: FailureContext) -> str:
        """Build failure summary section"""
        return f"""FAILURE CONTEXT:
- Failed Action: {failure_context.failed_action}
- Error Message: {failure_context.error_message}
- Platform: {failure_context.platform}
- Screen Size: {failure_context.window_size[0]}x{failure_context.window_size[1]}
- Device Info: {failure_context.device_info.get('device_name', 'Unknown')} ({failure_context.device_info.get('platform_version', 'Unknown')})
- App State: {failure_context.app_state.get('current_activity', 'Unknown')}"""
    
    def _build_platform_context(self, platform: str) -> str:
        """Build platform-specific context"""
        if platform not in self.platform_guidance:
            return f"PLATFORM: {platform} (Limited platform-specific guidance available)"
        
        guidance = self.platform_guidance[platform]
        
        context = f"""PLATFORM CONTEXT ({platform}):
- Preferred Locators: {', '.join(guidance['locator_preferences'])}
- Common Interactions: {', '.join(guidance['interaction_patterns'])}
- Known Issues: {'; '.join(guidance['common_issues'])}"""
        
        return context
    
    def _build_recovery_guidance(self, platform: str, include_examples: bool = True) -> str:
        """Build recovery guidance section"""
        guidance = """RECOVERY STRATEGY GUIDANCE:

1. IMMEDIATE FIXES:
   - Check if target element exists with different attributes
   - Look for alternative elements that achieve the same goal
   - Consider if scrolling or swiping might reveal the element
   - Check for modal dialogs or overlays blocking interaction

2. TIMING STRATEGIES:
   - Add explicit waits for elements to appear
   - Wait for animations to complete
   - Check for loading states or progress indicators

3. ALTERNATIVE APPROACHES:
   - Find elements by text content instead of IDs
   - Use relative positioning from visible landmarks
   - Try different interaction methods (tap vs click)
   - Consider app navigation alternatives

4. DIAGNOSTIC ACTIONS:
   - Take additional screenshots for state verification
   - Capture updated page source after wait periods
   - Check app permissions and connectivity"""
        
        if include_examples:
            if platform == 'iOS':
                guidance += """

iOS-SPECIFIC EXAMPLES:
- For missing buttons: Try XPath with @name instead of @label
- For text fields: Use XCUIElementTypeTextField or XCUIElementTypeSecureTextField
- For keyboard issues: Add keyboard dismissal before interactions
- For timing: iOS animations often need 1-2 second waits"""
            elif platform == 'Android':
                guidance += """

ANDROID-SPECIFIC EXAMPLES:
- For missing elements: Try @text, @content-desc, or @resource-id
- For view hierarchy: Use UiSelector with className or textContains
- For keyboard: Check if soft keyboard is blocking elements
- For activities: Verify correct activity context is loaded"""
        
        return guidance
    
    def build_follow_up_prompt(self, 
                              previous_attempt: str,
                              new_error: str,
                              attempt_number: int) -> str:
        """Build follow-up prompt for subsequent healing attempts"""
        
        prompt = f"""FOLLOW-UP ANALYSIS - ATTEMPT #{attempt_number}

PREVIOUS ATTEMPT:
- Action Tried: {previous_attempt}
- New Error: {new_error}

This is a follow-up analysis after the previous healing attempt failed. Please:

1. Analyze why the previous suggestion didn't work
2. Consider if the app state has changed
3. Suggest alternative approaches
4. Increase wait times or add state verification steps

Focus on different strategies than the previous attempt. If this is attempt #3 or higher, consider:
- Fundamental navigation issues
- App state problems requiring restart
- Permissions or authentication issues
- Need for manual intervention

Please provide a more conservative approach with higher confidence thresholds."""

        return prompt
    
    def create_debugging_prompt(self, failure_context: FailureContext) -> str:
        """Create prompt for debugging complex failures"""
        
        return f"""DEBUGGING ANALYSIS REQUEST

This is a complex failure requiring detailed analysis. Please help debug:

FAILURE DETAILS:
- Action: {failure_context.failed_action}
- Error: {failure_context.error_message}
- Platform: {failure_context.platform}

DEBUGGING QUESTIONS:
1. What is the current app state visible in the screenshot?
2. Are there any error dialogs, permission requests, or unexpected screens?
3. Is the app in the expected state for the intended action?
4. Are there visual indicators of loading, processing, or network issues?
5. Could this be a timing issue where elements are not yet available?

Please provide a diagnostic assessment rather than immediate action suggestions.
Focus on identifying the root cause of the failure."""