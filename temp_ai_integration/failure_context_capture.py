"""
Failure Context Capture Module

Captures comprehensive failure context including screenshots and UI hierarchies
when Appium tests fail, preparing data for AI analysis.
"""

import os
import time
import logging
from typing import Dict, Any, Optional, Tuple
from dataclasses import dataclass
from appium.webdriver.webdriver import WebDriver
from selenium.common.exceptions import WebDriverException


@dataclass
class FailureContext:
    """Container for all failure context data"""
    screenshot_path: str
    page_source: str
    failed_action: str
    error_message: str
    timestamp: float
    device_info: Dict[str, Any]
    app_state: Dict[str, Any]
    window_size: Tuple[int, int]
    platform: str  # 'iOS' or 'Android'


class FailureContextCapture:
    """Captures comprehensive failure context for AI analysis"""
    
    def __init__(self, output_dir: str = "failure_contexts"):
        """
        Initialize failure context capture
        
        Args:
            output_dir: Directory to save screenshots and context files
        """
        self.output_dir = output_dir
        self.logger = logging.getLogger(__name__)
        
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Rate limiting to prevent spam
        self.last_capture_time = 0
        self.min_capture_interval = float(os.getenv('SCREENSHOT_FREQUENCY_LIMIT', '5.0'))
        
    def capture_failure_context(self,
                               driver: WebDriver,
                               failed_action: str,
                               error_message: str,
                               additional_context: Optional[Dict[str, Any]] = None) -> FailureContext:
        """
        Capture complete failure context including screenshot and UI hierarchy
        
        Args:
            driver: Appium WebDriver instance
            failed_action: Description of the action that failed
            error_message: Error message from Appium
            additional_context: Optional additional context information
            
        Returns:
            FailureContext object with all captured data
        """
        timestamp = time.time()
        
        # Rate limiting check
        if timestamp - self.last_capture_time < self.min_capture_interval:
            self.logger.debug("Skipping capture due to rate limiting")
            raise Exception("Rate limited: too many captures in short time")
        
        self.last_capture_time = timestamp
        
        try:
            # Generate unique filename based on timestamp
            filename_base = f"failure_{int(timestamp)}"
            screenshot_path = os.path.join(self.output_dir, f"{filename_base}.png")
            
            # Capture screenshot
            screenshot_data = self._capture_screenshot(driver, screenshot_path)
            
            # Get page source (XML hierarchy)
            page_source = self._capture_page_source(driver)
            
            # Get device and app information
            device_info = self._get_device_info(driver)
            app_state = self._get_app_state(driver)
            window_size = self._get_window_size(driver)
            platform = self._detect_platform(driver)
            
            # Create failure context object
            context = FailureContext(
                screenshot_path=screenshot_path,
                page_source=page_source,
                failed_action=failed_action,
                error_message=error_message,
                timestamp=timestamp,
                device_info=device_info,
                app_state=app_state,
                window_size=window_size,
                platform=platform
            )
            
            # Save context to file for debugging
            self._save_context_file(context, filename_base)
            
            self.logger.info(f"Captured failure context: {screenshot_path}")
            return context
            
        except Exception as e:
            self.logger.error(f"Failed to capture failure context: {e}")
            raise
    
    def _capture_screenshot(self, driver: WebDriver, screenshot_path: str) -> bool:
        """Capture screenshot and save to file"""
        try:
            # Check if screenshots are disabled
            if os.getenv('DISABLE_SCREENSHOTS', 'false').lower() == 'true':
                self.logger.debug("Screenshots disabled via environment variable")
                return False
            
            success = driver.save_screenshot(screenshot_path)
            if success:
                self.logger.debug(f"Screenshot saved: {screenshot_path}")
                return True
            else:
                self.logger.warning("Screenshot capture returned False")
                return False
                
        except WebDriverException as e:
            self.logger.error(f"WebDriver error capturing screenshot: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Unexpected error capturing screenshot: {e}")
            return False
    
    def _capture_page_source(self, driver: WebDriver) -> str:
        """Capture page source (XML hierarchy)"""
        try:
            source = driver.page_source
            self.logger.debug(f"Captured page source: {len(source)} characters")
            return source
        except WebDriverException as e:
            self.logger.error(f"WebDriver error capturing page source: {e}")
            return ""
        except Exception as e:
            self.logger.error(f"Unexpected error capturing page source: {e}")
            return ""
    
    def _get_device_info(self, driver: WebDriver) -> Dict[str, Any]:
        """Get device information"""
        try:
            capabilities = driver.capabilities
            return {
                'platform_name': capabilities.get('platformName', 'Unknown'),
                'platform_version': capabilities.get('platformVersion', 'Unknown'),
                'device_name': capabilities.get('deviceName', 'Unknown'),
                'device_udid': capabilities.get('udid', 'Unknown'),
                'automation_name': capabilities.get('automationName', 'Unknown'),
                'app_package': capabilities.get('appPackage', 'Unknown'),
                'app_activity': capabilities.get('appActivity', 'Unknown'),
                'bundle_id': capabilities.get('bundleId', 'Unknown')
            }
        except Exception as e:
            self.logger.warning(f"Could not get device info: {e}")
            return {}
    
    def _get_app_state(self, driver: WebDriver) -> Dict[str, Any]:
        """Get current app state information"""
        try:
            state = {}
            
            # Try to get current activity/context
            try:
                state['current_activity'] = driver.current_activity
            except:
                pass
            
            try:
                state['current_context'] = driver.current_context
            except:
                pass
            
            try:
                state['contexts'] = driver.contexts
            except:
                pass
            
            # Get orientation
            try:
                state['orientation'] = driver.orientation
            except:
                pass
            
            return state
            
        except Exception as e:
            self.logger.warning(f"Could not get app state: {e}")
            return {}
    
    def _get_window_size(self, driver: WebDriver) -> Tuple[int, int]:
        """Get window/screen size"""
        try:
            size = driver.get_window_size()
            return (size['width'], size['height'])
        except Exception as e:
            self.logger.warning(f"Could not get window size: {e}")
            return (0, 0)
    
    def _detect_platform(self, driver: WebDriver) -> str:
        """Detect if this is iOS or Android"""
        try:
            platform = driver.capabilities.get('platformName', '').lower()
            if 'ios' in platform:
                return 'iOS'
            elif 'android' in platform:
                return 'Android'
            else:
                return 'Unknown'
        except Exception:
            return 'Unknown'
    
    def _save_context_file(self, context: FailureContext, filename_base: str):
        """Save context information to JSON file for debugging"""
        try:
            import json
            
            context_file = os.path.join(self.output_dir, f"{filename_base}_context.json")
            
            # Convert context to serializable dict
            context_dict = {
                'screenshot_path': context.screenshot_path,
                'failed_action': context.failed_action,
                'error_message': context.error_message,
                'timestamp': context.timestamp,
                'device_info': context.device_info,
                'app_state': context.app_state,
                'window_size': context.window_size,
                'platform': context.platform,
                'page_source_length': len(context.page_source),
                'page_source_preview': context.page_source[:500] + "..." if len(context.page_source) > 500 else context.page_source
            }
            
            with open(context_file, 'w', encoding='utf-8') as f:
                json.dump(context_dict, f, indent=2, ensure_ascii=False)
            
            self.logger.debug(f"Context file saved: {context_file}")
            
        except Exception as e:
            self.logger.warning(f"Could not save context file: {e}")
    
    def cleanup_old_contexts(self, max_age_hours: int = 24):
        """Clean up old failure context files"""
        try:
            import glob
            
            cutoff_time = time.time() - (max_age_hours * 3600)
            
            # Find old files
            pattern = os.path.join(self.output_dir, "failure_*")
            for file_path in glob.glob(pattern):
                try:
                    if os.path.getmtime(file_path) < cutoff_time:
                        os.remove(file_path)
                        self.logger.debug(f"Cleaned up old context file: {file_path}")
                except Exception as e:
                    self.logger.warning(f"Could not remove old file {file_path}: {e}")
                    
        except Exception as e:
            self.logger.warning(f"Cleanup failed: {e}")