"""
Integration Example for Existing Mobile Automation Framework

This example shows how to integrate AI healing into your existing
MobileAppAutomation framework with minimal changes.
"""

import os
import sys
import logging
from typing import Dict, Any, Optional

# Add the AI integration module to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

from ai_healing_orchestrator import AIHealingOrchestrator, HealingResult


class EnhancedActionExecutor:
    """
    Enhanced version of your existing ActionExecutor with AI healing capabilities
    
    This can be used as a drop-in replacement for your current action executor
    or as a wrapper around it.
    """
    
    def __init__(self, original_executor=None, enable_ai_healing: bool = True):
        """
        Initialize enhanced action executor
        
        Args:
            original_executor: Your existing ActionExecutor instance
            enable_ai_healing: Whether to enable AI healing functionality
        """
        self.original_executor = original_executor
        self.enable_ai_healing = enable_ai_healing
        self.logger = logging.getLogger(__name__)
        
        # Initialize AI healing if enabled
        self.healing_orchestrator = None
        if self.enable_ai_healing:
            try:
                self.healing_orchestrator = AIHealingOrchestrator(
                    output_dir="ai_healing_outputs",
                    max_attempts=int(os.getenv('AI_HEALING_MAX_ATTEMPTS', '3')),
                    confidence_threshold=float(os.getenv('AI_HEALING_CONFIDENCE_THRESHOLD', '0.6'))
                )
                self.logger.info("AI healing orchestrator initialized successfully")
            except Exception as e:
                self.logger.warning(f"AI healing initialization failed: {e}")
                self.enable_ai_healing = False
    
    def execute_action(self, driver, action_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute action with AI healing fallback
        
        Args:
            driver: Appium WebDriver instance
            action_data: Action data dictionary (your existing format)
            
        Returns:
            Execution result with healing information if applicable
        """
        action_description = self._get_action_description(action_data)
        
        try:
            # Try original action execution first
            if self.original_executor:
                result = self.original_executor.execute_action(driver, action_data)
            else:
                result = self._execute_original_action(driver, action_data)
            
            # Add healing info to result
            result['ai_healing_used'] = False
            return result
            
        except Exception as original_error:
            self.logger.warning(f"Action failed: {action_description} - {original_error}")
            
            # Try AI healing if enabled
            if self.enable_ai_healing and self.healing_orchestrator:
                return self._attempt_ai_healing(driver, action_data, action_description, original_error)
            else:
                # Re-raise original error if healing is disabled
                raise original_error
    
    def _attempt_ai_healing(self, driver, action_data: Dict[str, Any], 
                           action_description: str, original_error: Exception) -> Dict[str, Any]:
        """Attempt AI healing for failed action"""
        try:
            self.logger.info(f"Attempting AI healing for: {action_description}")
            
            # Create retry function for original action
            def retry_original():
                if self.original_executor:
                    return self.original_executor.execute_action(driver, action_data)
                else:
                    return self._execute_original_action(driver, action_data)
            
            # Trigger AI healing
            healing_session = self.healing_orchestrator.heal_test_failure(
                driver=driver,
                failed_action=action_description,
                error_message=str(original_error),
                retry_original_action=retry_original
            )
            
            # Process healing results
            if healing_session.final_result == HealingResult.SUCCESS:
                self.logger.info(f"AI healing successful for: {action_description}")
                return {
                    'status': 'success',
                    'ai_healing_used': True,
                    'healing_session': healing_session,
                    'success_action': healing_session.success_action,
                    'attempts': len(healing_session.attempts),
                    'healing_time': healing_session.total_time
                }
            else:
                self.logger.warning(f"AI healing failed for: {action_description}")
                # Re-raise original error with healing context
                healing_info = {
                    'ai_healing_attempted': True,
                    'healing_result': healing_session.final_result.value,
                    'attempts': len(healing_session.attempts),
                    'healing_time': healing_session.total_time
                }
                
                # Attach healing info to exception
                original_error.healing_info = healing_info
                raise original_error
                
        except Exception as healing_error:
            self.logger.error(f"AI healing system error: {healing_error}")
            # Re-raise original error
            raise original_error
    
    def _get_action_description(self, action_data: Dict[str, Any]) -> str:
        """Extract human-readable action description from action data"""
        # Adapt this to your action data format
        action_type = action_data.get('action_type', 'unknown')
        
        if action_type == 'click':
            element = action_data.get('element', {})
            element_text = element.get('text', element.get('name', 'unknown element'))
            return f"Click on '{element_text}'"
        elif action_type == 'type':
            text = action_data.get('text', 'text')
            return f"Type '{text}'"
        elif action_type == 'swipe':
            direction = action_data.get('direction', 'unknown direction')
            return f"Swipe {direction}"
        else:
            return f"Execute {action_type} action"
    
    def _execute_original_action(self, driver, action_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Basic action execution logic (replace with your actual implementation)
        
        This is a simplified example - replace with your actual action execution logic
        """
        action_type = action_data.get('action_type')
        
        if action_type == 'click':
            element_locator = action_data.get('locator')
            element = driver.find_element(element_locator['by'], element_locator['value'])
            element.click()
            return {'status': 'success', 'action': 'click'}
            
        elif action_type == 'type':
            element_locator = action_data.get('locator')
            text = action_data.get('text')
            element = driver.find_element(element_locator['by'], element_locator['value'])
            element.clear()
            element.send_keys(text)
            return {'status': 'success', 'action': 'type'}
            
        else:
            raise NotImplementedError(f"Action type '{action_type}' not implemented")


def integrate_with_existing_framework():
    """
    Example of how to integrate with your existing framework
    """
    # Option 1: Wrapper approach (recommended)
    # from app.action_executor import ActionExecutor  # Your existing executor
    # original_executor = ActionExecutor()
    # enhanced_executor = EnhancedActionExecutor(original_executor=original_executor)
    
    # Option 2: Direct replacement approach
    enhanced_executor = EnhancedActionExecutor(enable_ai_healing=True)
    
    return enhanced_executor


def example_usage_in_test():
    """Example of how this would be used in a test"""
    from appium import webdriver
    from appium.options.android import UiAutomator2Options
    
    # Set up your Appium driver (this is your existing setup)
    options = UiAutomator2Options()
    options.platform_name = "Android"
    options.device_name = "Android Emulator"
    options.app_package = "com.example.app"
    options.app_activity = ".MainActivity"
    
    driver = webdriver.Remote("http://127.0.0.1:4723", options=options)
    
    # Initialize enhanced executor
    executor = integrate_with_existing_framework()
    
    try:
        # Your existing action data format
        action_data = {
            'action_type': 'click',
            'locator': {
                'by': 'xpath',
                'value': '//android.widget.Button[@text="Login"]'
            },
            'element': {
                'text': 'Login',
                'type': 'button'
            }
        }
        
        # Execute action with AI healing fallback
        result = executor.execute_action(driver, action_data)
        
        if result.get('ai_healing_used'):
            print(f"✅ Action succeeded with AI healing!")
            print(f"   Healing attempts: {result['attempts']}")
            print(f"   Success action: {result['success_action']}")
            print(f"   Healing time: {result['healing_time']:.2f}s")
        else:
            print("✅ Action succeeded normally")
            
    except Exception as e:
        if hasattr(e, 'healing_info'):
            print(f"❌ Action failed even after AI healing attempt")
            print(f"   Healing result: {e.healing_info['healing_result']}")
            print(f"   Attempts made: {e.healing_info['attempts']}")
        else:
            print(f"❌ Action failed: {e}")
    
    finally:
        driver.quit()


if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(level=logging.INFO)
    
    # Run example
    print("🚀 AI Healing Integration Example")
    print("This example shows how to integrate AI healing with your existing framework")
    
    # Check if API key is configured
    if not os.getenv('TOGETHER_API_KEY'):
        print("❌ TOGETHER_API_KEY not found - AI healing will be disabled")
    else:
        print("✅ Together AI key found - AI healing will be enabled")
    
    # Show integration example (without actually running it)
    executor = integrate_with_existing_framework()
    stats = executor.healing_orchestrator.get_healing_statistics() if executor.healing_orchestrator else {}
    
    print(f"AI Healing Status: {'Enabled' if stats.get('enabled', False) else 'Disabled'}")
    print(f"Max Attempts: {stats.get('max_attempts', 'N/A')}")
    print(f"Confidence Threshold: {stats.get('confidence_threshold', 'N/A')}")
    
    print("\n📖 To integrate with your framework:")
    print("1. Import EnhancedActionExecutor from this module")
    print("2. Pass your existing ActionExecutor as original_executor parameter")
    print("3. Replace your executor calls with enhanced_executor.execute_action()")
    print("4. Handle the enhanced return format in your results processing")
    
    print("\n🔧 Minimal integration example:")
    print("""
    from temp_ai_integration.integration_example import EnhancedActionExecutor
    from your_app.action_executor import ActionExecutor
    
    # Wrap your existing executor
    original_executor = ActionExecutor()
    enhanced_executor = EnhancedActionExecutor(original_executor)
    
    # Use enhanced executor instead of original
    result = enhanced_executor.execute_action(driver, action_data)
    """)