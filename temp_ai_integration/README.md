# AI-Powered Test Healing Integration

## Overview

This AI healing integration adds intelligent test failure recovery to your mobile automation framework using multimodal LLMs (Large Language Models) like Llama Vision. When test steps fail, the system captures the failure context (screenshot + UI hierarchy), analyzes it with AI, and suggests recovery actions.

## Features

- 🔍 **Automatic Failure Detection**: Captures screenshots and UI hierarchies when tests fail
- 🧠 **AI Analysis**: Uses Together AI's Llama Vision to analyze failure contexts  
- 🎯 **Smart Suggestions**: Provides specific XPath/locator suggestions and action recommendations
- 🔄 **Retry Logic**: Attempts multiple healing strategies with confidence-based filtering
- 📊 **Comprehensive Logging**: Tracks healing attempts and success rates
- 🌐 **Cross-Platform**: Supports both iOS and Android automation

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Test Failure  │───▶│ Failure Context  │───▶│ UI Element      │
│                 │    │ Capture          │    │ Parser          │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ Action Executor │◀───│ AI Healing       │◀───│ Prompt Builder  │
│                 │    │ Orchestrator     │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │ Together AI      │    │ Multimodal LLM  │
                       │ Client           │    │ (Llama Vision)  │
                       └──────────────────┘    └─────────────────┘
```

## Quick Start

### 1. Installation

```bash
# Install dependencies
pip install -r temp_ai_integration/requirements.txt

# Set up environment variables
cp .env.template .env
# Edit .env and add your TOGETHER_API_KEY
```

### 2. Basic Usage

```python
from temp_ai_integration import AIHealingOrchestrator

# Initialize the healing system
orchestrator = AIHealingOrchestrator(
    max_attempts=3,
    confidence_threshold=0.6
)

# In your test when a failure occurs:
try:
    driver.find_element(By.XPATH, "//button[@text='Login']").click()
except Exception as e:
    # Trigger AI healing
    healing_session = orchestrator.heal_test_failure(
        driver=driver,
        failed_action="Click Login button",
        error_message=str(e)
    )
    
    if healing_session.final_result == HealingResult.SUCCESS:
        print("✅ Test healing successful!")
    else:
        print("❌ Healing failed, manual intervention required")
```

### 3. Integration with Existing Framework

```python
# Example integration with your action executor
class EnhancedActionExecutor:
    def __init__(self):
        self.healing_orchestrator = AIHealingOrchestrator()
    
    def execute_action(self, action_data):
        try:
            # Your original action execution logic
            return self.original_execute_action(action_data)
        except Exception as e:
            # Trigger healing on failure
            healing_session = self.healing_orchestrator.heal_test_failure(
                driver=self.driver,
                failed_action=action_data.get('description', 'Unknown action'),
                error_message=str(e),
                retry_original_action=lambda: self.original_execute_action(action_data)
            )
            
            if healing_session.final_result == HealingResult.SUCCESS:
                return {"status": "healed", "session": healing_session}
            else:
                raise e  # Re-raise if healing failed
```

## Configuration

### Environment Variables

```bash
# Required
TOGETHER_API_KEY=your_together_ai_api_key_here

# Optional AI Configuration
AI_HEALING_ENABLED=true
AI_HEALING_MAX_ATTEMPTS=3
AI_HEALING_CONFIDENCE_THRESHOLD=0.6

# Performance Settings
SCREENSHOT_FREQUENCY_LIMIT=5.0
SCREENSHOT_MAX_PER_MINUTE=12
DISABLE_SCREENSHOTS=false
```

### Programmatic Configuration

```python
orchestrator = AIHealingOrchestrator(
    together_api_key="your_key",
    output_dir="healing_outputs",
    max_attempts=3,
    confidence_threshold=0.6
)
```

## Components Deep Dive

### 1. Together AI Client (`together_ai_client.py`)

Handles communication with Together AI's multimodal models:

```python
client = TogetherAIClient()

# Test connection
if client.test_connection():
    # Analyze failure with image and context
    response = client.analyze_failure_context(
        screenshot_path="failure.png",
        ui_elements=parsed_elements,
        failed_action="Click Login",
        error_message="Element not found"
    )
    
    print(f"Suggestion: {response.suggested_action}")
    print(f"Confidence: {response.confidence}")
    print(f"XPaths: {response.xpath_suggestions}")
```

### 2. Failure Context Capture (`failure_context_capture.py`)

Captures comprehensive failure context:

```python
capture = FailureContextCapture(output_dir="failures")

context = capture.capture_failure_context(
    driver=driver,
    failed_action="Login attempt",
    error_message="Element not clickable"
)

print(f"Screenshot: {context.screenshot_path}")
print(f"Platform: {context.platform}")
print(f"Page source length: {len(context.page_source)}")
```

### 3. UI Element Parser (`ui_element_parser.py`)

Extracts actionable elements from XML:

```python
parser = UIElementParser()

elements = parser.parse_ui_elements(page_source, platform="iOS")

# Filter high-priority elements
high_priority = parser.filter_elements_by_criteria(
    elements,
    min_score=0.7,
    max_elements=10
)

# Generate alternative XPaths
for element in high_priority:
    alt_xpaths = parser.generate_alternative_xpaths(element)
    print(f"Element: {element.text}")
    print(f"XPaths: {alt_xpaths}")
```

### 4. Prompt Builder (`prompt_builder.py`)

Creates optimized prompts for AI analysis:

```python
builder = PromptBuilder()

prompt_context = builder.build_analysis_prompt(
    failure_context=context,
    ui_elements=elements,
    max_elements=15
)

combined_prompt = builder.combine_prompt_components(prompt_context)
```

### 5. AI Healing Orchestrator (`ai_healing_orchestrator.py`)

Main coordinator that combines all components:

```python
orchestrator = AIHealingOrchestrator()

# Main healing entry point
session = orchestrator.heal_test_failure(
    driver=driver,
    failed_action="Click submit button",
    error_message="ElementNotInteractableException"
)

# Check results
if session.final_result == HealingResult.SUCCESS:
    print(f"Healed in {len(session.attempts)} attempts")
    print(f"Success action: {session.success_action}")
```

## Advanced Usage

### Custom Action Handlers

```python
class CustomActionHandler:
    def handle_swipe_action(self, driver, direction, distance):
        # Custom swipe implementation
        pass
    
    def handle_wait_action(self, driver, element_xpath, timeout):
        # Custom wait implementation  
        pass

# Register custom handlers
orchestrator.register_action_handler('swipe', CustomActionHandler().handle_swipe_action)
orchestrator.register_action_handler('wait', CustomActionHandler().handle_wait_action)
```

### Platform-Specific Configurations

```python
# iOS-specific configuration
ios_orchestrator = AIHealingOrchestrator()
ios_orchestrator.set_platform_preferences(
    platform='iOS',
    preferred_locators=['name', 'label', 'type'],
    interaction_delays={'tap': 0.5, 'type': 1.0}
)

# Android-specific configuration  
android_orchestrator = AIHealingOrchestrator()
android_orchestrator.set_platform_preferences(
    platform='Android', 
    preferred_locators=['resource-id', 'text', 'content-desc'],
    interaction_delays={'click': 0.3, 'type': 0.8}
)
```

### Healing Session Analysis

```python
# Analyze healing performance
def analyze_healing_sessions(sessions):
    success_rate = sum(1 for s in sessions if s.final_result == HealingResult.SUCCESS) / len(sessions)
    avg_attempts = sum(len(s.attempts) for s in sessions) / len(sessions)
    avg_time = sum(s.total_time for s in sessions) / len(sessions)
    
    print(f"Success rate: {success_rate:.2%}")
    print(f"Average attempts: {avg_attempts:.1f}")
    print(f"Average time: {avg_time:.2f}s")

# Get session statistics
stats = orchestrator.get_healing_statistics()
print(f"Healing enabled: {stats['enabled']}")
print(f"Max attempts: {stats['max_attempts']}")
```

## Testing and Validation

### Running the Demo

```bash
# Test all components
python temp_ai_integration/demo_ai_healing.py

# Test specific components
python -c "
from temp_ai_integration import TogetherAIClient
client = TogetherAIClient()
print('Connection OK:', client.test_connection())
"
```

### Unit Tests

```bash
# Run tests
cd temp_ai_integration
python -m pytest tests/ -v

# Test with coverage
python -m pytest tests/ --cov=. --cov-report=html
```

### Integration Tests

```bash
# Test with real Appium (requires device)
python demo_ai_healing.py --real-device --platform android

# Test with mock Appium
python demo_ai_healing.py --mock-only
```

## Performance Considerations

### Rate Limiting

- Screenshots are rate-limited (default: max 1 per 5 seconds)
- AI API calls respect Together AI rate limits
- Healing attempts are limited per failure (default: 3 attempts)

### Memory Management

```python
# Clean up old failure contexts
capture = FailureContextCapture()
capture.cleanup_old_contexts(max_age_hours=24)

# Monitor memory usage
import psutil
process = psutil.Process()
print(f"Memory usage: {process.memory_info().rss / 1024 / 1024:.1f} MB")
```

### Performance Monitoring

```python
# Enable performance metrics
os.environ['ENABLE_PERFORMANCE_METRICS'] = 'true'

# Monitor healing performance
healing_metrics = orchestrator.get_performance_metrics()
print(f"Average healing time: {healing_metrics['avg_healing_time']:.2f}s")
print(f"Success rate: {healing_metrics['success_rate']:.2%}")
```

## Troubleshooting

### Common Issues

1. **Together AI Connection Failed**
   ```bash
   # Check API key
   echo $TOGETHER_API_KEY
   
   # Test connection
   curl -H "Authorization: Bearer $TOGETHER_API_KEY" https://api.together.xyz/v1/models
   ```

2. **Screenshot Capture Failed**
   ```python
   # Check Appium driver state
   print(f"Driver session: {driver.session_id}")
   print(f"Page source length: {len(driver.page_source)}")
   
   # Test manual screenshot
   driver.save_screenshot("test_screenshot.png")
   ```

3. **UI Element Parsing Issues**
   ```python
   # Debug page source
   with open("debug_page_source.xml", "w") as f:
       f.write(driver.page_source)
   
   # Check element counts
   parser = UIElementParser()
   elements = parser.parse_ui_elements(driver.page_source)
   print(f"Found {len(elements)} elements")
   ```

4. **Low AI Confidence Scores**
   ```python
   # Lower confidence threshold temporarily
   orchestrator.confidence_threshold = 0.3
   
   # Add more context to prompts
   prompt_context.additional_context = "App is in login screen, user needs to authenticate"
   ```

### Debug Mode

```python
# Enable debug logging
import logging
logging.getLogger('temp_ai_integration').setLevel(logging.DEBUG)

# Enable mock responses for testing
os.environ['MOCK_AI_RESPONSES'] = 'true'
```

## Integration Checklist

- [ ] Together AI API key configured
- [ ] Dependencies installed
- [ ] Demo script runs successfully  
- [ ] Unit tests pass
- [ ] Integration with existing action executor
- [ ] Performance monitoring enabled
- [ ] Error handling and logging configured
- [ ] Rate limiting configured appropriately
- [ ] Documentation updated for team

## Future Enhancements

1. **Enhanced Vision Models**: Support for additional multimodal models
2. **Learning System**: Track successful healing patterns for improved suggestions
3. **Custom Training**: Fine-tune models on project-specific UI patterns
4. **Advanced OCR**: Integrate OCR for better text recognition
5. **Performance Optimization**: Caching and optimization for faster healing
6. **UI Element Classification**: Better categorization of interactive elements

## Support

For issues and questions:
1. Check the troubleshooting section above
2. Review demo script and logs
3. Test individual components in isolation
4. Check Together AI API status and quotas

## License

This AI integration module is part of the MobileAppAutomation project and follows the same licensing terms.