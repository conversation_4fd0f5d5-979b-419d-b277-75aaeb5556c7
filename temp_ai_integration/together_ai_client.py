"""
Together AI Client for Multimodal LLM Integration

This module provides an interface to Together AI's API for accessing
Llama Vision and other multimodal models for test healing.
"""

import os
import base64
import logging
from typing import Dict, Any, Optional, List
import requests
from dataclasses import dataclass


@dataclass
class AIResponse:
    """Response from AI model containing suggested actions and confidence"""
    suggested_action: str
    confidence: float
    reasoning: str
    xpath_suggestions: List[str]
    action_type: str  # 'click', 'type', 'swipe', 'wait', etc.


class TogetherAIClient:
    """Client for interacting with Together AI's multimodal models"""
    
    def __init__(self, api_key: Optional[str] = None, base_url: Optional[str] = None):
        """
        Initialize Together AI client
        
        Args:
            api_key: Together AI API key (defaults to env var TOGETHER_API_KEY)
            base_url: API base URL (defaults to env var TOGETHER_BASE_URL)
        """
        self.api_key = api_key or os.getenv('TOGETHER_API_KEY')
        self.base_url = base_url or os.getenv('TOGETHER_BASE_URL', 'https://api.together.xyz/v1')
        
        if not self.api_key:
            raise ValueError("Together AI API key not found. Set TOGETHER_API_KEY environment variable")
        
        self.logger = logging.getLogger(__name__)
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        })
        
        # Default model - Llama Vision or multimodal model
        self.model_name = "meta-llama/Llama-3.2-11B-Vision-Instruct-Turbo"
        
    def encode_image(self, image_path: str) -> str:
        """
        Encode image to base64 for API transmission
        
        Args:
            image_path: Path to screenshot image
            
        Returns:
            Base64 encoded image string
        """
        try:
            with open(image_path, "rb") as image_file:
                encoded = base64.b64encode(image_file.read()).decode('utf-8')
                return f"data:image/png;base64,{encoded}"
        except Exception as e:
            self.logger.error(f"Failed to encode image {image_path}: {e}")
            raise
    
    def analyze_failure_context(self, 
                               screenshot_path: str,
                               ui_elements: List[Dict[str, Any]],
                               failed_action: str,
                               error_message: str,
                               additional_context: Optional[str] = None) -> AIResponse:
        """
        Analyze failure context using multimodal LLM
        
        Args:
            screenshot_path: Path to failure screenshot
            ui_elements: List of parsed UI elements with XPath/locators
            failed_action: Description of the action that failed
            error_message: Appium error message
            additional_context: Optional additional context
            
        Returns:
            AIResponse with suggested healing actions
        """
        try:
            # Encode screenshot
            image_data = self.encode_image(screenshot_path)
            
            # Build structured prompt
            prompt = self._build_analysis_prompt(
                ui_elements, failed_action, error_message, additional_context
            )
            
            # Prepare API request
            payload = {
                "model": self.model_name,
                "messages": [
                    {
                        "role": "system",
                        "content": self._get_system_prompt()
                    },
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": prompt
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": image_data
                                }
                            }
                        ]
                    }
                ],
                "max_tokens": 1000,
                "temperature": 0.3,  # Lower temperature for more focused responses
                "stream": False
            }
            
            # Make API call
            response = self.session.post(
                f"{self.base_url}/chat/completions",
                json=payload,
                timeout=60
            )
            response.raise_for_status()
            
            # Parse response
            result = response.json()
            ai_text = result['choices'][0]['message']['content']
            
            # Parse AI response into structured format
            return self._parse_ai_response(ai_text)
            
        except Exception as e:
            self.logger.error(f"Failed to analyze failure context: {e}")
            raise
    
    def _get_system_prompt(self) -> str:
        """Get system prompt for AI model"""
        return """You are an expert mobile app automation engineer specializing in test healing and recovery.
        
Your task is to analyze mobile app screenshots and UI element hierarchies to suggest specific actions when automation tests fail.

You should respond in this exact JSON format:
{
    "suggested_action": "Brief description of recommended action",
    "confidence": 0.85,
    "reasoning": "Detailed explanation of why this action should work",
    "xpath_suggestions": ["//XCUIElementTypeButton[@name='Login']", "//android.widget.Button[@text='Login']"],
    "action_type": "click"
}

Action types can be: click, type, swipe, wait, scroll, double_tap, long_press

Focus on:
1. Identifying the most likely alternative elements to interact with
2. Suggesting precise XPath/locator strategies
3. Providing confidence scores based on visual analysis
4. Explaining your reasoning clearly
5. Considering iOS vs Android platform differences"""

    def _build_analysis_prompt(self, 
                              ui_elements: List[Dict[str, Any]], 
                              failed_action: str,
                              error_message: str,
                              additional_context: Optional[str] = None) -> str:
        """Build structured prompt for AI analysis"""
        
        elements_text = "\n".join([
            f"- {elem.get('type', 'Unknown')}: '{elem.get('text', 'No text')}' "
            f"[xpath=\"{elem.get('xpath', 'No xpath')}\"] "
            f"[bounds=\"{elem.get('bounds', 'No bounds')}\"] "
            f"[clickable={elem.get('clickable', False)}]"
            for elem in ui_elements[:20]  # Limit to first 20 elements
        ])
        
        prompt = f"""MOBILE APP AUTOMATION FAILURE ANALYSIS

FAILED ACTION: {failed_action}
ERROR MESSAGE: {error_message}

AVAILABLE UI ELEMENTS:
{elements_text}

SCREENSHOT: See attached image showing current app state

ADDITIONAL CONTEXT: {additional_context or 'None'}

Please analyze the screenshot and UI elements to suggest the best recovery action. Consider:
1. What element should we interact with instead?
2. Is the target element visible but with different attributes?
3. Do we need to scroll or navigate to find the element?
4. Is there a timing issue (element not ready)?
5. Are there alternative paths to achieve the same goal?

Respond in the specified JSON format with specific XPath suggestions."""

        return prompt
    
    def _parse_ai_response(self, ai_text: str) -> AIResponse:
        """Parse AI response into structured format"""
        try:
            import json
            import re
            
            # Try to extract JSON from response
            json_match = re.search(r'\{.*\}', ai_text, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                data = json.loads(json_str)
                
                return AIResponse(
                    suggested_action=data.get('suggested_action', 'No suggestion provided'),
                    confidence=float(data.get('confidence', 0.5)),
                    reasoning=data.get('reasoning', 'No reasoning provided'),
                    xpath_suggestions=data.get('xpath_suggestions', []),
                    action_type=data.get('action_type', 'unknown')
                )
            else:
                # Fallback parsing for non-JSON responses
                return AIResponse(
                    suggested_action=ai_text[:200],
                    confidence=0.5,
                    reasoning=ai_text,
                    xpath_suggestions=[],
                    action_type='unknown'
                )
                
        except Exception as e:
            self.logger.warning(f"Failed to parse AI response as JSON: {e}")
            return AIResponse(
                suggested_action=ai_text[:200],
                confidence=0.3,
                reasoning=f"Parse error: {str(e)}",
                xpath_suggestions=[],
                action_type='unknown'
            )
    
    def test_connection(self) -> bool:
        """Test connection to Together AI API"""
        try:
            response = self.session.get(f"{self.base_url}/models")
            return response.status_code == 200
        except Exception as e:
            self.logger.error(f"Connection test failed: {e}")
            return False