# AI Healing Integration Requirements
# Install with: pip install -r requirements.txt

# Core Appium dependencies (should already be installed in main project)
Appium-Python-Client>=3.0.0
selenium>=4.15.0

# HTTP requests for API communication
requests>=2.31.0

# Environment variable loading
python-dotenv>=1.0.0

# JSON handling (built-in json module is usually sufficient)
# But some advanced JSON parsing might benefit from:
ujson>=5.8.0

# Image processing (for screenshot handling)
Pillow>=10.0.0

# Logging enhancements
colorlog>=6.7.0

# Optional: Advanced text processing
nltk>=3.8.1

# Optional: Computer vision for enhanced image analysis
# opencv-python>=4.8.0

# Optional: OCR capabilities
# pytesseract>=0.3.10

# Development and testing dependencies
pytest>=7.4.0
pytest-asyncio>=0.21.0
mock>=5.1.0

# Documentation
Sphinx>=7.2.0
sphinx-rtd-theme>=1.3.0

# Code quality
flake8>=6.1.0
black>=23.9.0
isort>=5.12.0

# Type checking
mypy>=1.6.0
types-requests>=2.31.0

# Performance monitoring
memory-profiler>=0.61.0
psutil>=5.9.0