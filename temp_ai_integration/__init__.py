"""
AI-Powered Test Healing Module

This module provides AI-driven test healing capabilities for mobile automation
using multimodal LLMs like Llama Vision to analyze failure contexts and suggest
recovery actions.

Key Components:
- together_ai_client: Interface to Together AI's Llama Vision model
- failure_context_capture: Captures screenshots and UI hierarchies on failures
- ui_element_parser: Extracts actionable elements from XML/page source
- prompt_builder: Creates optimized prompts for multimodal LLM
- ai_healing_orchestrator: Main orchestrator for healing attempts
"""

__version__ = "0.1.0"
__author__ = "Mobile Automation Team"

from .together_ai_client import TogetherAIClient
from .failure_context_capture import FailureContextCapture
from .ui_element_parser import UIElementParser
from .prompt_builder import Prompt<PERSON>uilder
from .ai_healing_orchestrator import AIHealingOrchestrator

__all__ = [
    "TogetherAIClient",
    "FailureContextCapture", 
    "UIElementParser",
    "PromptBuilder",
    "AIHealingOrchestrator"
]