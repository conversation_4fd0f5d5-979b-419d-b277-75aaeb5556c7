#!/usr/bin/env python3
"""
Simple test script for AI integration functionality
"""

import os
import sys
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_together_ai_connection():
    """Test Together AI connection"""
    print("🔍 Testing Together AI connection...")
    
    try:
        from temp_ai_integration.together_ai_client import TogetherAIClient
        
        client = TogetherAIClient()
        
        # Test API key availability
        api_key = os.getenv('TOGETHER_API_KEY')
        if not api_key:
            print("❌ TOGETHER_API_KEY not found in environment")
            return False
        
        print(f"✅ API key found: {api_key[:10]}...")
        
        # Test connection
        if client.test_connection():
            print("✅ Together AI connection successful!")
            return True
        else:
            print("❌ Together AI connection failed!")
            return False
            
    except Exception as e:
        print(f"❌ Error testing connection: {e}")
        return False

def test_ui_element_parsing():
    """Test UI element parsing"""
    print("\n🔍 Testing UI element parsing...")
    
    try:
        from temp_ai_integration.ui_element_parser import UIElementParser
        
        # Sample iOS XML
        ios_xml = """<?xml version="1.0" encoding="UTF-8"?>
<XCUIElementTypeApplication type="XCUIElementTypeApplication" name="TestApp" enabled="true" visible="true" x="0" y="0" width="375" height="812">
    <XCUIElementTypeButton type="XCUIElementTypeButton" name="Login" enabled="true" visible="true" x="50" y="300" width="275" height="44"/>
    <XCUIElementTypeTextField type="XCUIElementTypeTextField" name="Username" enabled="true" visible="true" x="50" y="200" width="275" height="44"/>
</XCUIElementTypeApplication>"""
        
        parser = UIElementParser()
        elements = parser.parse_ui_elements(ios_xml, 'iOS')
        
        print(f"✅ Parsed {len(elements)} UI elements")
        for elem in elements[:2]:
            print(f"   - {elem.element_type}: '{elem.text}' (score: {elem.interaction_score:.2f})")
        
        return True
        
    except Exception as e:
        print(f"❌ UI parsing test failed: {e}")
        return False

def test_prompt_building():
    """Test prompt building"""
    print("\n🔍 Testing prompt building...")
    
    try:
        from temp_ai_integration.prompt_builder import PromptBuilder
        from temp_ai_integration.failure_context_capture import FailureContext
        from temp_ai_integration.ui_element_parser import UIElement
        import time
        
        # Create mock context
        mock_context = FailureContext(
            screenshot_path="/tmp/test.png",
            page_source="<mock>test</mock>",
            failed_action="Click Login",
            error_message="Element not found",
            timestamp=time.time(),
            device_info={'device_name': 'Test Device'},
            app_state={'activity': 'Test'},
            window_size=(375, 812),
            platform='iOS'
        )
        
        # Create mock element
        mock_elements = [
            UIElement(
                element_type='Button',
                text='Login',
                resource_id='login_btn',
                class_name='Button',
                xpath='//Button[@name="Login"]',
                bounds='0,0,100,50',
                clickable=True,
                enabled=True,
                focused=False,
                visible=True,
                content_desc='Login button',
                package='',
                coordinates=(0, 0, 100, 50),
                center_point=(50, 25),
                platform='iOS',
                interaction_score=0.9
            )
        ]
        
        builder = PromptBuilder()
        prompt_context = builder.build_analysis_prompt(mock_context, mock_elements)
        combined_prompt = builder.combine_prompt_components(prompt_context)
        
        print(f"✅ Generated prompt: {len(combined_prompt)} characters")
        return True
        
    except Exception as e:
        print(f"❌ Prompt building test failed: {e}")
        return False

def test_orchestrator_initialization():
    """Test orchestrator initialization"""
    print("\n🔍 Testing orchestrator initialization...")
    
    try:
        from temp_ai_integration.ai_healing_orchestrator import AIHealingOrchestrator
        
        orchestrator = AIHealingOrchestrator(
            output_dir="test_outputs",
            max_attempts=2,
            confidence_threshold=0.5
        )
        
        stats = orchestrator.get_healing_statistics()
        print(f"✅ Orchestrator initialized successfully")
        print(f"   Enabled: {stats['enabled']}")
        print(f"   Max attempts: {stats['max_attempts']}")
        print(f"   Confidence threshold: {stats['confidence_threshold']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Orchestrator initialization failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 AI Integration Test Suite")
    print("=" * 50)
    
    tests = [
        test_together_ai_connection,
        test_ui_element_parsing,
        test_prompt_building,
        test_orchestrator_initialization
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test_func.__name__} crashed: {e}")
    
    print(f"\n📊 Test Results: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All tests passed! AI integration is ready to use.")
    else:
        print("⚠️  Some tests failed. Check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)