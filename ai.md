<img src="https://r2cdn.perplexity.ai/pplx-full-logo-primary-dark%402x.png" style="height:64px;margin-right:32px"/>

# I want to use AI to improve the flakyness in my test automation project using appium. I want to use open source multimodal open source LLMs like llama vision. When i action fails in during my test, I want to pass the image and the screen source page/ xml heirarchy in case of iOS and want LLM to analyze the information and suggest me the action to be taken like click on a xpath or locator and instruct appium driver to take that action. Can you guide me in details how I can integrate this in my framework and guide AI agent to code in python

To integrate an open-source multimodal LLM like Llama Vision with Appium in your Python test automation framework to analyze failure states and suggest remedies, you’ll need to set up a pipeline that sends screenshots and screen hierarchies (XML for iOS) to the LLM and receives suggestions for possible actions (like which XPath to click). Here’s a detailed guide to help you structure and implement this system:

***

## 1. Capture Failure Context from Appium

When a test step fails, capture both:

- **Screenshot:** Use Appium's `get_screenshot_as_png()` for the failure screen.
- **XML Hierarchy:** On iOS, fetch the source (`driver.page_source`) for the UI hierarchy. This gives all visible element attributes and structure.


## 2. Prepare Inputs for Multimodal LLM

The LLM needs both the image and a clear, structured screen representation. Research like LELANTE and the Macaw-LLM project recommend refining the XML/UI description to only the interactive/actionable elements and pairing it with the screenshot. You might want to pre-process the XML:[^1][^2][^3]

- Parse out only buttons, fields, clickable elements, etc.
- Map each with unique identifiers (like XPath or accessibility ids).


## 3. Construct the Multimodal Prompt

Combine the screenshot and the refined, structured XML into a “prompt” for your multimodal LLM:

- **Image:** Attach or encode the screenshot.
- **Structured text:** Provide a list/summary of actionable elements with their properties and location (XPath, ids).

Here’s a simplified example:

```
<image:screenshot.png>
Screen Elements:
- Button: 'Submit' [xpath="//XCUIElementTypeButton[@name='Submit']"]
- TextField: 'Username' [xpath="//XCUIElementTypeTextField[@name='Username']"]
Failed action: Clicked 'Submit'
Appium error: Element not clickable
What should I try next?
```


## 4. Model Integration

Pick an open-source multimodal LLM (e.g., Llama 3 Vision via Ollama, AnyModal, Macaw-LLM). Many frameworks (including AnyModal) provide Python APIs to feed images and text:[^4][^5][^6][^1]

- Set up the Python client for your chosen model.
- Pass the image and screen context as described above.
- Receive model suggestions (e.g., “Try clicking the ‘Username’ field”, “Scroll down and retry ‘Submit’”).

Example using Ollama (Llama 3 Vision) in Python:[^5]

```python
import ollama

# Ensure you have 'llama3-vision' pulled to your local ollama setup
ollama.pull('llama3-vision')

# Prepare the prompt (see above)
with open('screenshot.png', 'rb') as img_file:
    image_bytes = img_file.read()
prompt = """
Screen Elements:
... # pre-processed XML summary
Failed action: ...
Appium error: ...
What should I try next?
"""

response = ollama.chat(
    model='llama3-vision',
    messages=[
        {"role": "user", "content": prompt, "images": [image_bytes]}
    ]
)
suggested_action = response['message']['content']
```


## 5. Automate Action Selection and Execution

Parse the LLM response for actionable instructions (XPath, locator type), and use Appium to attempt the suggested recovery:

```python
# Assume suggested_action contains something like "Try clicking //XCUIElementTypeTextField[@name='Username']"
xpath = extract_xpath_from_llm_response(suggested_action)
driver.find_element_by_xpath(xpath).click()  # Or other Appium action
```

Depending on response quality, you may implement simple parsing rules or fine-tune your prompt structure to favor predictable suggestions.

## 6. Loop, Logging, and Human Review

For persistent failures or ambiguous suggestions, log all inputs, LLM outputs, and actions attempted so you can review and improve your pipeline. This helps you refine prompts or add fallback logic.

***

## Integration Summary Table

| Step | Purpose | Technology/Tool |
| :-- | :-- | :-- |
| Capture screenshot, XML | Failure context collection | Appium (driver.get_screenshot_as_png, driver.page_source) |
| Refine XML | Focus on actionable elements | Custom Python XML Parser |
| Create multimodal prompt | Structure context for LLM | Manual formatting/Python dict |
| Invoke Vision LLM | Generate suggested recovery actions | Ollama (Llama Vision), AnyModal, Macaw-LLM |
| Parse action/execute | Carry out suggested action | Appium Python Driver |
| Log \& review | Improve system over iterations | Python logging, review scripts |


***

## Key References \& Inspiration

- Macaw-LLM, CogniSim mobile adapters use UI string parsers to convert app screens into LLM-friendly representations.[^2][^1]
- LELANTE paper details a robust Android LLM integration pipeline, including XML refinement and icon classification—applicable to iOS too.[^3]
- AnyModal Python framework allows for extension and custom multimodal integration, usable for vision+UI setups.[^4]

***

You should start with basic integration and iterate to improve prompt structure, extraction accuracy, and LLM fine-tuning to suit your specific app and failure cases. Let me know if you'd like example code for specific parts or guidance for a particular open-source LLM setup!
<span style="display:none">[^10][^7][^8][^9]</span>

<div style="text-align: center">⁂</div>

[^1]: https://github.com/lyuchenyang/Macaw-LLM

[^2]: https://github.com/RevylAI/CogniSim

[^3]: https://arxiv.org/html/2504.20896v1

[^4]: https://www.reddit.com/r/Python/comments/1gtbrzb/anymodal_a_python_framework_for_multimodal_llms/

[^5]: https://towardsdatascience.com/multimodal-models-llms-that-can-see-and-hear-5c6737c981d3/

[^6]: https://www.bentoml.com/blog/multimodal-ai-a-guide-to-open-source-vision-language-models

[^7]: https://python.plainenglish.io/8-powerful-ways-to-build-a-multimodal-ai-system-that-understands-images-and-text-540fb51b5fca

[^8]: https://www.youtube.com/watch?v=1vIqjQEFmjE

[^9]: https://arxiv.org/pdf/2402.09171.pdf

[^10]: https://www.reddit.com/r/RedditEng/comments/1ap4axo/from_fragile_to_agile_automating_the_fight/

